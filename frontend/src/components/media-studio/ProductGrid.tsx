import React, { useState, useMemo } from "react";
import { cn } from "@/lib/utils";
import { Checkbox } from "@/components/ui/checkbox";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent } from "@/components/ui/card";
import { Copy, Image, Video, Wand2, CheckCircle, Search, ZoomIn, X } from "lucide-react";
import { Asset } from "@/services/mediaService";
import { MainTab } from "@/types/mediaStudio";

import { PromptEditor } from "./PromptEditor";

// Define Product interface that matches what's used in media studio
interface Product {
  id: string;
  title: string;
  variants: any[];
  collections: Array<{ id: string; name: string; color: string }>;
  assets: Asset[];
}

interface ProductGridProps {
  products: Product[];
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string>;
  onPromptChange: (productId: string, value: string) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onSelectAllProducts: (isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  availableCollections?: Array<{ id: string; name: string; color: string }>;
  collectionFilters?: string[];
  onCollectionFiltersChange?: (filters: string[]) => void;
  searchQuery?: string;
  onSearchQueryChange?: (query: string) => void;
  filterSelectedOnly?: boolean;
  onFilterSelectedOnlyChange?: (value: boolean) => void;
  filterHasGenerated?: boolean;
  onFilterHasGeneratedChange?: (value: boolean) => void;
  sortMode?: "default" | "selected_first" | "generated_first";
  onSortModeChange?: (
    mode: "default" | "selected_first" | "generated_first"
  ) => void;
  productTotalCount?: number;
}

const ProductRow: React.FC<{
  product: Product;
  selectedAssetIds: Set<string>;
  onAssetSelect: (asset: Asset, isMultiSelect: boolean) => void;
  prompts: Record<string, string>;
  onPromptChange: (productId: string, value: string) => void;
  onTabChange: (tab: MainTab) => void;
  selectedProductIds: Set<string>;
  onProductSelectionChange: (productId: string, isChecked: boolean) => void;
  onCopyPromptToAll: (sourceProductId: string) => void;
  generatedImages?: Record<string, Asset[]>;
  allAvailableAssets?: Asset[];
}> = ({
  product,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onCopyPromptToAll,
  generatedImages = {},
  allAvailableAssets = [],
}) => {
  const isRowSelected = selectedProductIds.has(product.id);
  const hasPlaceholder = React.useMemo(
    () => product.assets.some((a) => a.id.startsWith("temp_")),
    [product.assets]
  );

  // Modal state for image zoom
  const [isModalOpen, setIsModalOpen] = React.useState(false);
  const [selectedImageIndex, setSelectedImageIndex] = React.useState(0);

  const existingAssetIds = React.useMemo(
    () => new Set(product.assets.map((a) => a.id)),
    [product.assets]
  );

  const productGeneratedImages = generatedImages[product.id] || [];
  const filteredGeneratedImages = React.useMemo(
    () => productGeneratedImages.filter((a) => !existingAssetIds.has(a.id)),
    [productGeneratedImages, existingAssetIds]
  );

  // Get all available assets for this product (original + generated)
  const allProductAssets = React.useMemo(() => {
    return [...product.assets, ...filteredGeneratedImages];
  }, [product.assets, filteredGeneratedImages]);

  return (
    <Card
      className={cn(
        "group transition-all duration-200 hover:shadow-md",
        isRowSelected
          ? "ring-2 ring-primary/30 border-primary/50 shadow-lg"
          : "",
        hasPlaceholder ? "animate-pulse" : ""
      )}
    >
      <CardContent className="p-0">
        <div className="grid grid-cols-12 gap-0">
          {/* Column 1: Product Info */}
          <div className="col-span-4 p-4 border-r border-border/50">
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-foreground text-sm leading-tight mb-1 truncate">
                  {product.title}
                </h3>
              </div>
              <Checkbox
                checked={isRowSelected}
                onCheckedChange={(checked) =>
                  onProductSelectionChange(product.id, checked as boolean)
                }
                className="mt-0.5 w-4 h-4"
              />
            </div>

            <div className="flex items-center gap-2">
              <div className="flex items-center gap-1">
                {allProductAssets.length > 0 && (
                  <div className="flex items-center gap-1">
                    {allProductAssets.slice(0, 2).map((asset) => (
                      <div
                        key={asset.id}
                        className={cn(
                          "w-4 h-4 rounded border flex items-center justify-center text-[10px]",
                          asset.type === "image"
                            ? "bg-primary/10 border-primary/20 text-primary"
                            : "bg-accent/10 border-accent/20 text-accent"
                        )}
                      >
                        {asset.type === "image" ? (
                          <Image className="w-2.5 h-2.5" />
                        ) : (
                          <Video className="w-2.5 h-2.5" />
                        )}
                      </div>
                    ))}
                    {allProductAssets.length > 2 && (
                      <span className="text-xs text-muted-foreground font-medium">
                        +{allProductAssets.length - 2}
                      </span>
                    )}
                  </div>
                )}
              </div>
              <span className="text-xs text-muted-foreground">
                {allProductAssets.length} asset
                {allProductAssets.length !== 1 ? "s" : ""}
              </span>
            </div>

            {/* Categories */}
            {(product.collections ?? []).length > 0 && (
              <div className="flex flex-wrap gap-1.5 mb-4">
                {(product.collections ?? []).slice(0, 3).map((col) => (
                  <Badge
                    key={col.id}
                    variant="secondary"
                    className="text-xs px-2 py-0.5"
                  >
                    {col.name}
                  </Badge>
                ))}
                {(product.collections ?? []).length > 3 && (
                  <Badge variant="secondary" className="text-xs px-2 py-0.5">
                    +{(product.collections ?? []).length - 3}
                  </Badge>
                )}
              </div>
            )}

            {/* Product Media Thumbnails */}
            {allProductAssets.length > 0 && (
              <div className="grid grid-cols-4 gap-2 mb-4">
                {allProductAssets.slice(0, 6).map((asset, index) => (
                  <div
                    key={asset.id}
                    className={cn(
                      "relative aspect-square border rounded-md cursor-pointer transition-all duration-200 overflow-hidden group/image",
                      selectedAssetIds.has(asset.id)
                        ? "border-primary ring-1 ring-primary/20 shadow-sm"
                        : "border-border hover:border-primary/50 hover:shadow-sm"
                    )}
                    onClick={() => onAssetSelect(asset, true)}
                  >
                    {asset.type === "image" ? (
                      <img
                        src={asset.url}
                        alt={asset.filename}
                        className="w-full h-full object-cover transition-transform duration-200 group-hover:scale-105"
                        onError={(e) => {
                          e.currentTarget.src =
                            "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiAxMk0xMiAxMnoiIHN0cm9rZT0iIzk3OTdhNyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+";
                        }}
                      />
                    ) : (
                      <div className="w-full h-full bg-gradient-to-br from-muted to-muted/80 flex items-center justify-center">
                        <Video className="w-3 h-3 text-muted-foreground" />
                      </div>
                    )}

                    {/* Hover overlay for selection indication */}
                    <div className="absolute inset-0 bg-primary/5 opacity-0 group-hover/image:opacity-100 transition-opacity duration-200 flex items-center justify-center">
                      <div className="bg-white/90 backdrop-blur-sm rounded-full px-2 py-1 text-xs font-medium text-primary">
                        Click to select
                      </div>
                    </div>

                    {/* Magnifying glass button */}
                    {asset.type === "image" && (
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          setSelectedImageIndex(index);
                          setIsModalOpen(true);
                        }}
                        className="absolute bottom-1 right-1 w-6 h-6 bg-black/60 hover:bg-black/80 rounded-full flex items-center justify-center opacity-0 group-hover/image:opacity-100 transition-opacity duration-200"
                        title="Zoom image"
                      >
                        <ZoomIn className="w-3 h-3 text-white" />
                      </button>
                    )}

                    {/* Selection indicator */}
                    {selectedAssetIds.has(asset.id) && (
                      <div className="absolute -top-0.5 -right-0.5 w-4 h-4 bg-primary rounded-full flex items-center justify-center shadow-sm">
                        <CheckCircle className="w-2.5 h-2.5 text-white" />
                      </div>
                    )}

                    {/* Generation indicator */}
                    {asset.id.startsWith("temp_") && (
                      <div className="absolute inset-0 bg-primary/10 flex items-center justify-center">
                        <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin"></div>
                      </div>
                    )}
                  </div>
                ))}
                {allProductAssets.length > 6 && (
                  <div className="aspect-square border-2 border-dashed border-border rounded-md flex items-center justify-center bg-muted/50">
                    <span className="text-xs text-muted-foreground font-medium">
                      +{allProductAssets.length - 6}
                    </span>
                  </div>
                )}
              </div>
            )}

          </div>

          {/* Column 2: Prompt Box */}
          <div className="col-span-7 p-5 border-r border-border/50">
            <div className="space-y-3">
              <div className="flex items-center justify-between">
                <label className="text-sm font-medium text-foreground">
                  Generation Prompt
                </label>
                <div className="flex items-center gap-2">
                  <div className="text-xs text-muted-foreground">
                    {prompts[product.id]?.length || 0} characters
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => onCopyPromptToAll(product.id)}
                    className="text-xs h-7 px-2"
                    title="Copy prompt to all products"
                  >
                    <Copy className="w-3 h-3" />
                  </Button>
                </div>
              </div>
              <PromptEditor
                value={prompts[product.id] || ""}
                onChange={(value) => onPromptChange(product.id, value)}
                placeholder="Describe the image or video you want to create for this product..."
                availableAssets={allAvailableAssets}
                className="min-h-[120px] border-border focus-within:border-primary focus-within:ring-1 focus-within:ring-primary/20"
              />
            </div>
          </div>

          {/* Column 3: Add-ons & Actions */}
          <div className="col-span-1 p-2">
            <div className="space-y-3">
              <div className="text-sm font-medium text-foreground mb-3">
                Enhancements
              </div>
              <div className="space-y-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTabChange("models")}
                  className="w-full justify-start text-xs h-8 hover:bg-primary/5"
                >
                  + Models
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTabChange("props")}
                  className="w-full justify-start text-xs h-8 hover:bg-primary/5"
                >
                  + Props
                </Button>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => onTabChange("scenes")}
                  className="w-full justify-start text-xs h-8 hover:bg-primary/5"
                >
                  + Scenes
                </Button>
              </div>

              {/* Status indicators */}
              <div className="pt-3 border-t border-border/50">
                <div className="flex items-center gap-2">
                  {hasPlaceholder ? (
                    <div className="flex items-center gap-2 text-amber-600">
                      <div className="w-2 h-2 bg-amber-500 rounded-full animate-pulse"></div>
                      <span className="text-xs font-medium">Generating...</span>
                    </div>
                  ) : productGeneratedImages.length > 0 ? (
                    <div className="flex items-center gap-2 text-green-600">
                      <CheckCircle className="w-3 h-3" />
                      <span className="text-xs font-medium">Ready</span>
                    </div>
                  ) : (
                    <div className="flex items-center gap-2 text-muted-foreground">
                      <div className="w-2 h-2 bg-muted-foreground rounded-full"></div>
                      <span className="text-xs font-medium">Draft</span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </CardContent>

      {/* Image Zoom Modal */}
      {isModalOpen && (
        <div className="fixed inset-0 bg-black/80 backdrop-blur-sm z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-[90vh] w-full">
            {/* Close button */}
            <button
              onClick={() => setIsModalOpen(false)}
              className="absolute -top-12 right-0 w-8 h-8 bg-white/10 hover:bg-white/20 rounded-full flex items-center justify-center text-white transition-colors"
              title="Close"
            >
              <X className="w-5 h-5" />
            </button>

            {/* Main image */}
            <div className="bg-white rounded-lg overflow-hidden shadow-2xl">
              <div className="relative aspect-video bg-gray-100">
                {allProductAssets[selectedImageIndex]?.type === "image" ? (
                  <img
                    src={allProductAssets[selectedImageIndex].url}
                    alt={allProductAssets[selectedImageIndex].filename}
                    className="w-full h-full object-contain"
                    onError={(e) => {
                      e.currentTarget.src =
                        "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjQiIGhlaWdodD0iMjQiIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik0xMiAxMk0xMiAxMnoiIHN0cm9rZT0iIzk3OTdhNyIgc3Ryb2tlLXdpZHRoPSIyIiBzdHJva2UtbGluZWNhcD0icm91bmQiIHN0cm9rZS1saW5lam9pbj0icm91bmQiLz4KPC9zdmc+";
                    }}
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-muted to-muted/80 flex items-center justify-center">
                    <Video className="w-12 h-12 text-muted-foreground" />
                  </div>
                )}
              </div>

              {/* Thumbnail strip */}
              {allProductAssets.length > 1 && (
                <div className="p-4 bg-gray-50 border-t">
                  <div className="flex items-center gap-2 overflow-x-auto">
                    {allProductAssets.map((asset, index) => (
                      <button
                        key={asset.id}
                        onClick={() => setSelectedImageIndex(index)}
                        className={cn(
                          "flex-shrink-0 w-16 h-16 rounded-md overflow-hidden border-2 transition-all",
                          selectedImageIndex === index
                            ? "border-primary ring-2 ring-primary/20"
                            : "border-gray-200 hover:border-primary/50"
                        )}
                      >
                        {asset.type === "image" ? (
                          <img
                            src={asset.url}
                            alt={asset.filename}
                            className="w-full h-full object-cover"
                            onError={(e) => {
                              e.currentTarget.src =
                                "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMTYiIGhlaWdodD0iMTYiIHZpZXdCb3g9IjAgMCAxNiAxNiIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjNGNEY2Ii8+CjxwYXRoIGQ9Ik04IDhNOCA4eiIgc3Ryb2tlPSIjOTc5N2E3IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPjwvc3ZnPg==";
                            }}
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-muted to-muted/80 flex items-center justify-center">
                            <Video className="w-4 h-4 text-muted-foreground" />
                          </div>
                        )}
                      </button>
                    ))}
                  </div>
                </div>
              )}

              {/* Image info */}
              <div className="p-4 bg-white border-t">
                <div className="flex items-center justify-between">
                  <div>
                    <h3 className="font-semibold text-gray-900">{product.title}</h3>
                    <p className="text-sm text-gray-600">
                      {allProductAssets[selectedImageIndex]?.filename || `Image ${selectedImageIndex + 1}`}
                    </p>
                  </div>
                  <div className="text-sm text-gray-500">
                    {selectedImageIndex + 1} of {allProductAssets.length}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}
    </Card>
  );
};

export const ProductGrid: React.FC<ProductGridProps> = ({
  products,
  selectedAssetIds,
  onAssetSelect,
  prompts,
  onPromptChange,
  onTabChange,
  selectedProductIds,
  onProductSelectionChange,
  onSelectAllProducts,
  onCopyPromptToAll,
  generatedImages = {},
  availableCollections = [],
  collectionFilters = [],
  onCollectionFiltersChange,
  searchQuery = "",
  onSearchQueryChange,
  filterSelectedOnly = false,
  onFilterSelectedOnlyChange,
  filterHasGenerated = false,
  onFilterHasGeneratedChange,
  sortMode = "default",
  onSortModeChange,
  productTotalCount,
}) => {
  const [filtersOpen, setFiltersOpen] = useState(false);

  // Computed values
  const isClientFilterActive =
    filterSelectedOnly || filterHasGenerated || collectionFilters.length > 0;
  const areAllProductsSelected =
    products.length > 0 && products.every((p) => selectedProductIds.has(p.id));

  return (
    <Card className="w-full h-full overflow-hidden">
      <CardContent className="p-0">
        {/* Grid Layout */}
        <div className="overflow-auto max-h-[calc(100vh-400px)] p-6">
          {products.length === 0 ? (
            <div className="flex flex-col items-center justify-center py-16 text-center">
              <div className="w-16 h-16 bg-muted rounded-full flex items-center justify-center mb-4">
                <Search className="w-8 h-8 text-muted-foreground" />
              </div>
              <h3 className="text-lg font-semibold text-foreground mb-2">
                No products found
              </h3>
              <p className="text-muted-foreground max-w-md">
                {searchQuery || collectionFilters.length > 0
                  ? "Try adjusting your search or filters to find more products."
                  : "Add some products to get started with media generation."}
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {products.map((product) => (
                <ProductRow
                  key={product.id}
                  product={product}
                  selectedAssetIds={selectedAssetIds}
                  onAssetSelect={onAssetSelect}
                  prompts={prompts}
                  onPromptChange={onPromptChange}
                  onTabChange={onTabChange}
                  selectedProductIds={selectedProductIds}
                  onProductSelectionChange={onProductSelectionChange}
                  onCopyPromptToAll={onCopyPromptToAll}
                  generatedImages={generatedImages}
                  allAvailableAssets={[]}
                />
              ))}
            </div>
          )}
        </div>

        {/* Spacer to prevent the floating bar from obscuring the last row */}
        <div className="h-40 flex-shrink-0" />
      </CardContent>
    </Card>
  );
};
