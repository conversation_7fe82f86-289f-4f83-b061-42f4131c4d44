"use client";

import React, { useState, useCallback, useEffect } from "react";
import { useLayout } from "@/contexts/LayoutContext";
import { cn } from "@/lib/utils";
import { productService, Product } from "@/services/productService";
import { mediaService, Asset } from "@/services/mediaService";
import {
  MainTab,
  GenerationMode,
  ImageSettings,
  VideoSettings,
  BatchState,
} from "@/types/mediaStudio";
import { MainTabs } from "@/components/media-studio/MainTabs";
import { ProductGrid } from "@/components/media-studio/ProductGrid";
import { PreviewPane } from "@/components/media-studio/PreviewPane";
import { FloatingGenerateBar } from "@/components/media-studio/FloatingGenerateBar";
import { PromptEditor } from "@/components/media-studio/PromptEditor";
import { CollectionsFilter } from "@/components/media-studio/CollectionsFilter";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Card, CardContent } from "@/components/ui/card";
import { Search, AlertCircle } from "lucide-react";
import { toast } from "sonner";

const MODELS = [
  { id: "banana", name: "Banana", type: "image" as const },
  { id: "banana-video", name: "Banana Video", type: "video" as const },
];

const MediaStudioPage: React.FC = () => {
  const { sidebarCollapsed, isMobile } = useLayout();

  // Core state
  const [products, setProducts] = useState<Product[]>([]);
  const [isLoadingProducts, setIsLoadingProducts] = useState(true);
  const [productsError, setProductsError] = useState<string | null>(null);

  // Selection state
  const [selectedAssetIds, setSelectedAssetIds] = useState<Set<string>>(
    new Set()
  );
  const [selectedProductIds, setSelectedProductIds] = useState<Set<string>>(
    new Set()
  );
  const [activeAsset, setActiveAsset] = useState<Asset | null>(null);

  // UI state
  const [activeMainTab, setActiveMainTab] = useState<MainTab>("canvas");
  const [generationMode, setGenerationMode] = useState<GenerationMode>("image");
  const [selectedModelId, setSelectedModelId] = useState<string>("banana");

  // Generation state
  const [settings, setSettings] = useState<ImageSettings | VideoSettings>({
    size: "1024x1024",
    guidance: 7.5,
    steps: 25,
    strength: 0.8,
    seed: Math.floor(Math.random() * 100000),
    upscale: true,
    safety: true,
    aspectRatio: "1:1",
    quality: "Standard",
  });

  // Prompts and filters
  const [prompts, setPrompts] = useState<Record<string, string>>({});
  const [collectionFilters, setCollectionFilters] = useState<string[]>([]);
  const [searchQuery, setSearchQuery] = useState<string>("");
  const [filterSelectedOnly, setFilterSelectedOnly] = useState<boolean>(false);
  const [filterHasGenerated, setFilterHasGenerated] = useState<boolean>(false);
  const [sortMode, setSortMode] = useState<
    "default" | "selected_first" | "generated_first"
  >("default");

  // Generation batch state
  const [generationBatch, setGenerationBatch] = useState<BatchState | null>(
    null
  );
  const [isInitiating, setIsInitiating] = useState(false);
  const [initiationMessage, setInitiationMessage] = useState<string | null>(
    null
  );
  const [errorMessage, setErrorMessage] = useState<string | null>(null);

  // Generated assets
  const [generatedImages, setGeneratedImages] = useState<
    Record<string, Asset[]>
  >({});

  // Load products
  useEffect(() => {
    const fetchProducts = async () => {
      try {
        setIsLoadingProducts(true);
        setProductsError(null);

        const response = await productService.getProducts(1, 50, "");
        if (response && response.items) {
          const transformedProducts: Product[] = response.items.map(
            (item: any) => {
              const assets: Asset[] = [];
              const productForNaming = {
                title: item.title || item.name || "Product",
              } as any;

              if (item.images && Array.isArray(item.images)) {
                item.images.forEach((img: any) => {
                  const asset: Asset = {
                    id: `asset_${img.id}`,
                    productId: item.product_id || item.id,
                    url: img.src_url || img.src,
                    type: "image" as const,
                    filename: img.src_url?.split("/").pop() || "image.jpg",
                    displayName: "",
                  };
                  asset.displayName = `${productForNaming.title} - Image`;
                  assets.push(asset);
                });
              }

              const collections = Array.isArray(item.collections)
                ? item.collections.map((c: any) => ({
                    id: String(c.id ?? c.slug ?? c.name ?? "collection"),
                    name: String(c.name ?? c.slug ?? "Collection"),
                    color: String(c.color ?? "bg-gray-500"),
                  }))
                : [];

              return {
                id: item.product_id || item.id,
                title: item.title || item.name,
                variants: item.variants || [],
                collections,
                assets,
              } as Product;
            }
          );

          setProducts(transformedProducts);

          // Initialize prompts
          const initialPrompts: Record<string, string> = {};
          transformedProducts.forEach((product) => {
            initialPrompts[product.id] =
              `A professional product shot of a ${product.title.toLowerCase()}, high-resolution, on a clean white background.`;
          });
          setPrompts(initialPrompts);
        } else {
          setProducts([]);
        }
      } catch (error) {
        console.error("Failed to fetch products:", error);
        setProductsError("Failed to load products. Please try again.");
      } finally {
        setIsLoadingProducts(false);
      }
    };

    fetchProducts();
  }, []);

  // Load generated assets
  useEffect(() => {
    const loadGeneratedAssets = async () => {
      try {
        const response = await mediaService.getGeneratedAssets({ limit: 200 });
        const byProduct: Record<string, Asset[]> = {};
        for (const a of response.items) {
          const productId = a.product_id;
          if (!productId) continue;
          if (!byProduct[productId]) byProduct[productId] = [];
          byProduct[productId].push(a);
        }
        setGeneratedImages(byProduct);
      } catch (error) {
        console.error("Failed to load generated assets:", error);
      }
    };

    loadGeneratedAssets();
  }, []);

  // Update settings when mode changes
  useEffect(() => {
    if (generationMode === "image") {
      setSettings({
        size: "1024x1024",
        guidance: 7.5,
        steps: 25,
        strength: 0.8,
        seed: Math.floor(Math.random() * 100000),
        upscale: true,
        safety: true,
        aspectRatio: "1:1",
        quality: "Standard",
      });
      setSelectedModelId(MODELS.filter((m) => !m.id.includes("veo"))[0].id);
    } else if (generationMode === "video") {
      setSettings({
        duration: 4,
        fps: 24,
        resolution: "1080p",
        aspectRatio: "16:9",
        motionStrength: 5,
        seed: Math.floor(Math.random() * 100000),
        audio: false,
        quality: "Standard",
      });
      setSelectedModelId("veo-3.0-generate-preview");
    } else if (generationMode === "text") {
      setSettings({
        duration: 4,
        fps: 24,
        resolution: "1080p",
        aspectRatio: "16:9",
        motionStrength: 5,
        seed: Math.floor(Math.random() * 100000),
        audio: false,
        quality: "Standard",
      });
      setSelectedModelId("gpt-4o");
    }
  }, [generationMode]);

  const handleAssetSelect = useCallback(
    (asset: Asset, isMultiSelect: boolean) => {
      setSelectedAssetIds((prevSelectedAssets: Set<string>) => {
        let isToggleOff = false;
        const newAssetSet = new Set(prevSelectedAssets);

        if (isMultiSelect) {
          if (newAssetSet.has(asset.id)) {
            newAssetSet.delete(asset.id);
            isToggleOff = true;
          } else {
            newAssetSet.add(asset.id);
            // In video mode, limit to max 1 selected image per row
            if (generationMode === "video" && asset.type === "image") {
              const row = products.find(
                (p: Product) => p.id === asset.productId
              );
              const imgIds = (row?.assets || [])
                .filter((a: any) => a.type === "image")
                .map((a: any) => a.id);
              for (const id of imgIds) {
                if (id !== asset.id) newAssetSet.delete(id);
              }
            }
          }
        } else {
          const productAssets =
            products
              .find((p: Product) => p.id === asset.productId)
              ?.assets.map((a: Asset) => a.id) || [];

          for (const assetId of productAssets) {
            newAssetSet.delete(assetId);
          }

          if (!(productAssets.length === 1 && productAssets[0] === asset.id)) {
            newAssetSet.add(asset.id);
          } else {
            isToggleOff = true;
          }
        }

        // Update product selection based on asset selection
        const productHasSelection = products
          .find((p) => p.id === asset.productId)
          ?.assets.some((a) => newAssetSet.has(a.id));

        setSelectedProductIds((prevSelectedProducts) => {
          const newProductsSet = new Set(prevSelectedProducts);
          if (productHasSelection) {
            newProductsSet.add(asset.productId);
          } else {
            newProductsSet.delete(asset.productId);
          }
          return newProductsSet;
        });

        return newAssetSet;
      });
    },
    [products, generationMode]
  );

  const handleProductSelectionChange = useCallback(
    (productId: string, isChecked: boolean) => {
      const product = products.find((p) => p.id === productId);
      if (!product) return;

      setSelectedProductIds((prev) => {
        const newSet = new Set(prev);
        if (isChecked) newSet.add(productId);
        else newSet.delete(productId);
        return newSet;
      });

      if (isChecked) {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            const imageAssets = product.assets.filter(
              (a) => a.type === "image"
            );
            const toAdd = imageAssets[0];
            if (toAdd) newSet.add(toAdd.id);
          } else {
            product.assets.forEach((asset) => newSet.add(asset.id));
          }
          return newSet;
        });
      } else {
        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          product.assets.forEach((asset) => newSet.delete(asset.id));
          return newSet;
        });
      }
    },
    [products, generationMode]
  );

  const handleSelectAllProducts = useCallback(
    (isChecked: boolean) => {
      if (isChecked) {
        const allProductIds = new Set(products.map((p) => p.id));
        setSelectedProductIds(allProductIds);

        setSelectedAssetIds((prev) => {
          const newSet = new Set(prev);
          if (generationMode === "video") {
            products.forEach((product) => {
              const firstImg = product.assets.find((a) => a.type === "image");
              if (firstImg) newSet.add(firstImg.id);
            });
          } else {
            products.forEach((product) => {
              product.assets.forEach((asset) => newSet.add(asset.id));
            });
          }
          return newSet;
        });
      } else {
        setSelectedProductIds(new Set());
        setSelectedAssetIds(new Set());
        setActiveAsset(null);
      }
    },
    [products, generationMode]
  );

  const handlePromptChange = useCallback((productId: string, value: string) => {
    setPrompts((prev: Record<string, string>) => ({
      ...prev,
      [productId]: value,
    }));
  }, []);

  const handleCopyPromptToAll = useCallback(
    (sourceProductId: string) => {
      const sourcePrompt = prompts[sourceProductId];
      if (sourcePrompt === undefined) return;

      setPrompts((prev: Record<string, string>) => {
        const newPrompts: Record<string, string> = { ...prev };
        products.forEach((p: Product) => {
          newPrompts[p.id] = sourcePrompt;
        });
        return newPrompts;
      });
    },
    [prompts, products]
  );

  const handleGenerate = useCallback(async () => {
    const selectedCount = selectedProductIds.size;
    if (selectedCount === 0) return;

    // Handle text mode differently - for now, just show a message
    if (generationMode === "text") {
      toast.info("Text generation is not yet implemented");
      return;
    }

    const modeCap = generationMode === "video" ? 3 : 5;
    if (selectedCount > modeCap) {
      return;
    }

    try {
      setIsInitiating(true);
      setInitiationMessage(
        `Batch started (${selectedCount} items) • Mode: ${generationMode} • Model: ${MODELS.find((m) => m.id === selectedModelId)?.name || selectedModelId}`
      );

      const ids = Array.from(selectedProductIds).slice(0, modeCap);
      const items = ids.map((productId: string) => {
        const product = products.find((p) => p.id === productId);
        const imageAssetIds = (product?.assets || [])
          .filter((a) => a.type === "image")
          .map((a) => a.id);
        const selectedInRow = Array.from(selectedAssetIds).filter((aid) =>
          imageAssetIds.includes(aid)
        );

        const urls: string[] = (() => {
          const productGeneratedImages = generatedImages[productId] || [];
          const allImages = [
            ...(product?.assets.filter((a) => a.type === "image") || []),
            ...productGeneratedImages.filter((a) => a.type === "image"),
          ];

          const firstImg = allImages.find((a) => a.type === "image");
          return firstImg ? [firstImg.url] : [];
        })();

        return {
          productId,
          prompt:
            prompts[productId] ||
            `Professional product shot of ${product?.title}`,
          referenceImageUrls: urls,
        };
      });

      const start = await mediaService.startGeneration({
        mode: generationMode as "image" | "video",
        model: selectedModelId,
        settings: settings,
        items,
      });

      const batchId: string = start.batch_id || start.id;
      setGenerationBatch({
        batchId,
        total: items.length,
        completed: 0,
        failed: 0,
        status: "processing",
      });

      toast.success(`Generation batch started: ${batchId}`);
    } catch (error: any) {
      console.error("Generation failed:", error);
      setErrorMessage(error?.message || "Failed to start generation");
      toast.error("Failed to start generation");
    } finally {
      setTimeout(() => {
        setIsInitiating(false);
        setInitiationMessage(null);
      }, 6000);
    }
  }, [
    selectedProductIds,
    generationMode,
    selectedModelId,
    settings,
    products,
    selectedAssetIds,
    prompts,
    generatedImages,
  ]);

  const clearSelection = useCallback(() => {
    setSelectedAssetIds(new Set());
    setSelectedProductIds(new Set());
    setActiveAsset(null);
  }, []);

  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.key === "Escape") {
        clearSelection();
      }
    };

    window.addEventListener("keydown", handleKeyDown);
    return () => window.removeEventListener("keydown", handleKeyDown);
  }, [clearSelection]);

  const overLimit =
    generationMode !== "text" &&
    selectedProductIds.size > (generationMode === "video" ? 3 : 5);
  const overLimitMessage = `Maximum ${generationMode === "video" ? 3 : 5} items allowed for ${generationMode} generation.`;

  return (
    <div className="h-screen flex flex-col bg-background font-sans text-foreground overflow-hidden">
      {/* Main Content */}
      <main className="flex flex-1 overflow-hidden">
        {/* Left Panel (2/3 width) */}
        <div className="w-2/3 flex flex-col border-r border-border bg-background">
          {/* Left Panel Header */}
          <div className="flex-shrink-0 border-b border-border bg-muted/50 px-6 py-4">
            <div className="flex items-center gap-4">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search products..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10 h-9"
                />
              </div>

              <CollectionsFilter
                options={[]}
                selectedIds={collectionFilters}
                onChange={setCollectionFilters}
                compact={true}
              />

              {/* Quick Filters */}
              <div className="flex items-center gap-2">
                <Button
                  variant={filterSelectedOnly ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterSelectedOnly(!filterSelectedOnly)}
                  className="h-9"
                >
                  Selected
                </Button>
                <Button
                  variant={filterHasGenerated ? "default" : "outline"}
                  size="sm"
                  onClick={() => setFilterHasGenerated(!filterHasGenerated)}
                  className="h-9"
                >
                  Generated
                </Button>
              </div>
            </div>
          </div>

          {/* Product Grid */}
          <div className="flex-1 overflow-auto">
            <ProductGrid
              products={products}
              selectedAssetIds={selectedAssetIds}
              onAssetSelect={handleAssetSelect}
              prompts={prompts}
              onPromptChange={handlePromptChange}
              onTabChange={setActiveMainTab}
              selectedProductIds={selectedProductIds}
              onProductSelectionChange={handleProductSelectionChange}
              onSelectAllProducts={handleSelectAllProducts}
              onCopyPromptToAll={handleCopyPromptToAll}
              generatedImages={generatedImages}
              availableCollections={[]}
              collectionFilters={collectionFilters}
              onCollectionFiltersChange={setCollectionFilters}
              searchQuery={searchQuery}
              onSearchQueryChange={setSearchQuery}
              filterSelectedOnly={filterSelectedOnly}
              onFilterSelectedOnlyChange={setFilterSelectedOnly}
              filterHasGenerated={filterHasGenerated}
              onFilterHasGeneratedChange={setFilterHasGenerated}
              sortMode={sortMode}
              onSortModeChange={setSortMode}
              productTotalCount={products.length}
            />
          </div>
        </div>

        {/* Right Panel (1/3 width) */}
        <div className="w-1/3 flex flex-col bg-background">
          {/* Right Panel Header */}
          <div className="flex-shrink-0 border-b border-border bg-muted/50">
            <div className="flex items-center px-6 py-3">
              {["Gallery", "Models", "Props", "Scenes", "Brandbook"].map(
                (tab) => (
                  <Button
                    key={tab}
                    variant="ghost"
                    onClick={() =>
                      setActiveMainTab(tab.toLowerCase() as MainTab)
                    }
                    className={`px-4 py-2.5 text-sm font-medium transition-all duration-200 relative ${
                      activeMainTab === tab.toLowerCase()
                        ? "text-primary"
                        : "text-muted-foreground hover:text-foreground"
                    }`}
                  >
                    {tab}
                    {activeMainTab === tab.toLowerCase() && (
                      <div className="absolute bottom-0 left-0 right-0 h-0.5 bg-primary rounded-full"></div>
                    )}
                  </Button>
                )
              )}
            </div>
          </div>

          {/* Right Panel Content */}
          <div className="flex-1 overflow-auto">
            <div className="flex items-center justify-center h-full">
              <div className="text-center">
                <div className="text-red-500 text-lg font-semibold mb-2">
                  Failed to load!
                </div>
                <div className="text-gray-500 dark:text-gray-400">
                  Failed to fetch
                </div>
              </div>
            </div>
          </div>
        </div>
      </main>

      {/* Floating Generate Bar */}
      <FloatingGenerateBar
        selectedCount={selectedProductIds.size}
        generationMode={generationMode}
        onModeChange={setGenerationMode}
        settings={settings}
        setSettings={setSettings}
        selectedModelId={selectedModelId}
        onModelChange={setSelectedModelId}
        onGenerate={handleGenerate}
        generationBatch={generationBatch}
        isInitiating={isInitiating}
        initiationMessage={initiationMessage}
        overLimit={overLimit}
        overLimitMessage={overLimitMessage}
        errorMessage={errorMessage}
        onDismissMessage={() => setErrorMessage(null)}
        onDismissInitiationMessage={() => setInitiationMessage(null)}
      />
    </div>
  );
};

export default MediaStudioPage;
