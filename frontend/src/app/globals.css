@import url("https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&display=swap");
@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

@theme {
  /* Colors */
  --color-background: #ffffff;
  --color-foreground: #0f172a;

  --color-card: #ffffff;
  --color-card-foreground: #0f172a;

  --color-popover: #ffffff;
  --color-popover-foreground: #0f172a;

  /* Primary: Professional blue */
  --color-primary: #2563eb;
  --color-primary-foreground: #f8fafc;

  /* Secondary: Neutral gray */
  --color-secondary: #f1f5f9;
  --color-secondary-foreground: #0f172a;

  --color-muted: #f1f5f9;
  --color-muted-foreground: #64748b;

  /* Accent: Vibrant purple */
  --color-accent: #8b5cf6;
  --color-accent-foreground: #f8fafc;

  --color-destructive: #ef4444;
  --color-destructive-foreground: #f8fafc;

  --color-border: #e2e8f0;
  --color-input: #e2e8f0;
  --color-ring: #2563eb;

  /* Chart colors */
  --color-chart-1: #f97316;
  --color-chart-2: #06b6d4;
  --color-chart-3: #8b5cf6;
  --color-chart-4: #eab308;
  --color-chart-5: #ef4444;

  /* Border radius */
  --radius: 0.75rem;
}

@layer theme {
  .dark {
    /* Dark mode colors */
    --color-background: #0f172a;
    --color-foreground: #f8fafc;

    --color-card: #0f172a;
    --color-card-foreground: #f8fafc;

    --color-popover: #0f172a;
    --color-popover-foreground: #f8fafc;

    /* Primary: Brighter blue for dark mode */
    --color-primary: #3b82f6;
    --color-primary-foreground: #0f172a;

    /* Secondary: Darker gray */
    --color-secondary: #1e293b;
    --color-secondary-foreground: #f8fafc;

    --color-muted: #1e293b;
    --color-muted-foreground: #94a3b8;

    /* Accent: Adjusted for dark mode */
    --color-accent: #a855f7;
    --color-accent-foreground: #0f172a;

    --color-destructive: #f87171;
    --color-destructive-foreground: #0f172a;

    --color-border: #334155;
    --color-input: #1e293b;
    --color-ring: #3b82f6;

    /* Chart colors for dark mode */
    --color-chart-1: #fb923c;
    --color-chart-2: #22d3ee;
    --color-chart-3: #a855f7;
    --color-chart-4: #facc15;
    --color-chart-5: #f87171;
  }
}

/* Base styles */
@layer base {
  * {
    border-color: var(--color-border);
  }

  html, body {
    height: 100%;
    overflow: hidden;
  }

  body {
    color: var(--color-foreground);
    background: var(--color-background);
    font-feature-settings:
      "rlig" 1,
      "calt" 1;
    letter-spacing: var(--tracking-normal);
  }
}

/* Custom animations */
@keyframes accordion-down {
  from {
    height: 0;
  }
  to {
    height: var(--radix-accordion-content-height);
  }
}

@keyframes accordion-up {
  from {
    height: var(--radix-accordion-content-height);
  }
  to {
    height: 0;
  }
}

.animate-accordion-down {
  animation: accordion-down 0.2s ease-out;
}

.animate-accordion-up {
  animation: accordion-up 0.2s ease-out;
}

@theme inline {
  --font-sans: Geist, sans-serif;
  --font-mono: "Fira Code", "Courier New", monospace;
  --font-serif: "Lora", Georgia, serif;
  --radius: 0.5rem;
  --tracking-tighter: calc(var(--tracking-normal) - 0.05em);
  --tracking-tight: calc(var(--tracking-normal) - 0.025em);
  --tracking-wide: calc(var(--tracking-normal) + 0.025em);
  --tracking-wider: calc(var(--tracking-normal) + 0.05em);
  --tracking-widest: calc(var(--tracking-normal) + 0.1em);
  --tracking-normal: var(--tracking-normal);
  --shadow-2xl: var(--shadow-2xl);
  --shadow-xl: var(--shadow-xl);
  --shadow-lg: var(--shadow-lg);
  --shadow-md: var(--shadow-md);
  --shadow: var(--shadow);
  --shadow-sm: var(--shadow-sm);
  --shadow-xs: var(--shadow-xs);
  --shadow-2xs: var(--shadow-2xs);
  --spacing: var(--spacing);
  --letter-spacing: var(--letter-spacing);
  --shadow-offset-y: var(--shadow-offset-y);
  --shadow-offset-x: var(--shadow-offset-x);
  --shadow-spread: var(--shadow-spread);
  --shadow-blur: var(--shadow-blur);
  --shadow-opacity: var(--shadow-opacity);
  --color-shadow-color: var(--shadow-color);
  --color-sidebar-ring: var(--sidebar-ring);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar: var(--sidebar);
  --color-chart-5: var(--chart-5);
  --color-chart-4: var(--chart-4);
  --color-chart-3: var(--chart-3);
  --color-chart-2: var(--chart-2);
  --color-chart-1: var(--chart-1);
  --color-ring: var(--ring);
  --color-input: var(--input);
  --color-border: var(--border);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-destructive: var(--destructive);
  --color-accent-foreground: var(--accent-foreground);
  --color-accent: var(--accent);
  --color-muted-foreground: var(--muted-foreground);
  --color-muted: var(--muted);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-secondary: var(--secondary);
  --color-primary-foreground: var(--primary-foreground);
  --color-primary: var(--primary);
  --color-popover-foreground: var(--popover-foreground);
  --color-popover: var(--popover);
  --color-card-foreground: var(--card-foreground);
  --color-card: var(--card);
  --color-foreground: var(--foreground);
  --color-background: var(--background);
  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);
}

:root {
  --button-outline: rgba(0,0,0, .10);
  --badge-outline: rgba(0,0,0, .05);

  /* Automatic computation of border around primary / danger buttons */
  --opaque-button-border-intensity: -8; /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(0,0,0, .03);
  --elevate-2: rgba(0,0,0, .08);

  --background: 220 10% 98%;

  --foreground: 220 15% 12%;

  --border: 220 13% 91%;

  --card: 220 10% 96%;

  --card-foreground: 220 15% 12%;

  --card-border: 220 13% 89%;

  --sidebar: 220 10% 94%;

  --sidebar-foreground: 220 15% 12%;

  --sidebar-border: 220 13% 87%;

  --sidebar-primary: 142 86% 28%;

  --sidebar-primary-foreground: 142 20% 95%;

  --sidebar-accent: 220 12% 88%;

  --sidebar-accent-foreground: 220 15% 12%;

  --sidebar-ring: 250 84% 60%;

  --popover: 220 10% 92%;

  --popover-foreground: 220 15% 12%;

  --popover-border: 220 13% 85%;

  --primary: 142 86% 28%;

  --primary-foreground: 142 20% 95%;

  --secondary: 220 12% 86%;

  --secondary-foreground: 220 15% 12%;

  --muted: 220 8% 90%;

  --muted-foreground: 220 15% 42%;

  --accent: 250 84% 95%;

  --accent-foreground: 250 84% 20%;

  --destructive: 0 84% 60%;

  --destructive-foreground: 0 20% 95%;

  --input: 220 13% 75%;
  --ring: 250 84% 60%;
  --chart-1: 142 86% 28%;
  --chart-2: 250 84% 60%;
  --chart-3: 45 93% 58%;
  --chart-4: 0 84% 60%;
  --chart-5: 220 15% 35%;

  --font-sans: Inter, sans-serif;
  --font-serif: Georgia, serif;
  --font-mono: JetBrains Mono, monospace;
  --radius: .5rem; /* 8px */
  --shadow-2xs: 0px 2px 0px 0px hsl(220 10% 0% / 0.02);
  --shadow-xs: 0px 2px 0px 0px hsl(220 10% 0% / 0.05);
  --shadow-sm: 0px 2px 0px 0px hsl(220 10% 0% / 0.05), 0px 1px 2px -1px hsl(220 10% 0% / 0.10);
  --shadow: 0px 2px 0px 0px hsl(220 10% 0% / 0.05), 0px 1px 2px -1px hsl(220 10% 0% / 0.10);
  --shadow-md: 0px 2px 0px 0px hsl(220 10% 0% / 0.05), 0px 2px 4px -1px hsl(220 10% 0% / 0.15);
  --shadow-lg: 0px 2px 0px 0px hsl(220 10% 0% / 0.05), 0px 4px 6px -1px hsl(220 10% 0% / 0.15);
  --shadow-xl: 0px 2px 0px 0px hsl(220 10% 0% / 0.05), 0px 8px 10px -1px hsl(220 10% 0% / 0.20);
  --shadow-2xl: 0px 2px 0px 0px hsl(220 10% 0% / 0.25);
  --tracking-normal: 0em;
  --spacing: 0.25rem;

  /* Automatically computed borders - intensity can be controlled by the user by the --opaque-button-border-intensity setting */

  /* Fallback for older browsers */
  --sidebar-primary-border: hsl(var(--sidebar-primary));
  --sidebar-primary-border: hsl(from hsl(var(--sidebar-primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --sidebar-accent-border: hsl(var(--sidebar-accent));
  --sidebar-accent-border: hsl(from hsl(var(--sidebar-accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --primary-border: hsl(var(--primary));
  --primary-border: hsl(from hsl(var(--primary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --secondary-border: hsl(var(--secondary));
  --secondary-border: hsl(from hsl(var(--secondary)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --muted-border: hsl(var(--muted));
  --muted-border: hsl(from hsl(var(--muted)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --accent-border: hsl(var(--accent));
  --accent-border: hsl(from hsl(var(--accent)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);

  /* Fallback for older browsers */
  --destructive-border: hsl(var(--destructive));
  --destructive-border: hsl(from hsl(var(--destructive)) h s calc(l + var(--opaque-button-border-intensity)) / alpha);
}

.dark {
  --button-outline: rgba(255,255,255, .10);
  --badge-outline: rgba(255,255,255, .05);

  --opaque-button-border-intensity: 9;  /* In terms of percentages */

  /* Backgrounds applied on top of other backgrounds when hovered/active */
  --elevate-1: rgba(255,255,255, .04);
  --elevate-2: rgba(255,255,255, .09);

  --background: 220 15% 12%;

  --foreground: 220 10% 95%;

  --border: 220 15% 20%;

  --card: 220 15% 15%;

  --card-foreground: 220 10% 95%;

  --card-border: 220 15% 22%;

  --sidebar: 220 15% 10%;

  --sidebar-foreground: 220 10% 95%;

  --sidebar-border: 220 15% 18%;

  --sidebar-primary: 142 76% 36%;

  --sidebar-primary-foreground: 142 20% 95%;

  --sidebar-accent: 220 15% 18%;

  --sidebar-accent-foreground: 220 10% 95%;

  --sidebar-ring: 250 84% 60%;

  --popover: 220 15% 18%;

  --popover-foreground: 220 10% 95%;

  --popover-border: 220 15% 25%;

  --primary: 142 76% 36%;

  --primary-foreground: 142 20% 95%;

  --secondary: 220 15% 22%;

  --secondary-foreground: 220 10% 95%;

  --muted: 220 12% 16%;

  --muted-foreground: 220 8% 65%;

  --accent: 250 84% 20%;

  --accent-foreground: 250 84% 85%;

  --destructive: 0 84% 60%;

  --destructive-foreground: 0 20% 95%;

  --input: 220 15% 35%;
  --ring: 250 84% 60%;
  --chart-1: 142 76% 65%;
  --chart-2: 250 84% 75%;
  --chart-3: 45 93% 68%;
  --chart-4: 0 84% 70%;
  --chart-5: 220 10% 75%;

  --shadow-2xs: 0px 2px 0px 0px hsl(220 15% 0% / 0.20);
  --shadow-xs: 0px 2px 0px 0px hsl(220 15% 0% / 0.25);
  --shadow-sm: 0px 2px 0px 0px hsl(220 15% 0% / 0.25), 0px 1px 2px -1px hsl(220 15% 0% / 0.35);
  --shadow: 0px 2px 0px 0px hsl(220 15% 0% / 0.25), 0px 1px 2px -1px hsl(220 15% 0% / 0.35);
  --shadow-md: 0px 2px 0px 0px hsl(220 15% 0% / 0.25), 0px 2px 4px -1px hsl(220 15% 0% / 0.40);
  --shadow-lg: 0px 2px 0px 0px hsl(220 15% 0% / 0.25), 0px 4px 6px -1px hsl(220 15% 0% / 0.40);
  --shadow-xl: 0px 2px 0px 0px hsl(220 15% 0% / 0.25), 0px 8px 10px -1px hsl(220 15% 0% / 0.45);
  --shadow-2xl: 0px 2px 0px 0px hsl(220 15% 0% / 0.50);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
