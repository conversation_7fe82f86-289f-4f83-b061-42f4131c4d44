# ProductVideo Backend Environment Variables

# =============================================================================
# CORE APPLICATION SETTINGS
# =============================================================================
BASE_URL=http://localhost:8000
ENVIRONMENT=development
PORT=8000

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
DATABASE_URL=******************************************/ecommerce_db
TEST_DATABASE_URL=sqlite+aiosqlite:///./test.db

# =============================================================================
# SECURITY & AUTHENTICATION
# =============================================================================
SECRET_KEY=your-secret-key-change-in-production
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=30

# =============================================================================
# SHOPIFY APP CONFIGURATION
# =============================================================================
SHOPIFY_API_KEY=your_shopify_app_api_key
SHOPIFY_API_SECRET=your_shopify_app_secret_key

# =============================================================================
# OAUTH CONFIGURATION
# =============================================================================
GOOGLE_CLIENT_ID=your_google_client_id
GOOGLE_CLIENT_SECRET=your_google_client_secret
GITHUB_CLIENT_ID=your_github_client_id
GITHUB_CLIENT_SECRET=your_github_client_secret

# =============================================================================
# AI VIDEO GENERATION SERVICES
# =============================================================================
# Options: veo3, revid_ai, vidify, product_studio, mock
VIDEO_PROVIDER=veo3
VEO3_API_KEY=your_google_veo3_api_key
REVID_AI_API_KEY=your_revid_ai_api_key
PRODUCT_STUDIO_API_KEY=your_product_studio_api_key

# =============================================================================
# AI IMAGE GENERATION SERVICES
# =============================================================================
# Options: banana, dall_e, midjourney, mock
IMAGE_PROVIDER=banana
BANANA_API_KEY=your_banana_api_key

# =============================================================================
# STORAGE CONFIGURATION
# =============================================================================
# Options: s3, cloudflare_r2, google_cloud, local
STORAGE_PROVIDER=local
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_REGION=us-east-1
S3_BUCKET_NAME=your-productvideo-bucket
CLOUDFLARE_R2_ENDPOINT=https://your-account-id.r2.cloudflarestorage.com
CLOUDFLARE_R2_ACCESS_KEY=your_cloudflare_r2_access_key
CLOUDFLARE_R2_SECRET_KEY=your_cloudflare_r2_secret_key
CLOUDFLARE_R2_PUBLIC_DOMAIN=https://your-r2-public-domain.com
LOCAL_CDN_PATH=/app/media
LOCAL_CDN_URL=http://localhost:8000/media
LOCAL_STORAGE_PATH=./storage
LOCAL_STORAGE_BASE_URL=http://localhost:8000/storage
GCP_PROJECT_ID=your_gcp_project_id
GCP_BUCKET_NAME=your_gcp_bucket_name

# =============================================================================
# STRIPE CONFIGURATION
# =============================================================================
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret
STRIPE_STARTER_PRICE_ID=your_stripe_starter_price_id
STRIPE_PROFESSIONAL_PRICE_ID=your_stripe_professional_price_id
STRIPE_ENTERPRISE_PRICE_ID=your_stripe_enterprise_price_id
STRIPE_VIDEO_GENERATION_PRICE_ID=your_stripe_video_generation_price_id
STRIPE_STORAGE_PRICE_ID=your_stripe_storage_price_id
STRIPE_BANDWIDTH_PRICE_ID=your_stripe_bandwidth_price_id

# =============================================================================
# REDIS & JOB QUEUE CONFIGURATION
# =============================================================================
REDIS_URL=redis://localhost:6379

# =============================================================================
# CORS CONFIGURATION
# =============================================================================
CORS_ORIGINS=["http://localhost:3000", "http://localhost:5173"]
FRONTEND_URL=http://localhost:3000

# =============================================================================
# AIRBYTE CONFIGURATION
# =============================================================================
# Airbyte API Configuration
AIRBYTE_API_URL=http://localhost:8000
AIRBYTE_USER_ID=your_airbyte_user_id
AIRBYTE_PASSWORD=your_airbyte_password
AIRBYTE_WORKSPACE_ID=your_airbyte_workspace_id
AIRBYTE_DESTINATION_NAME=Postgres

# Airbyte Database Configuration (Separate from main app DB)
AIRBYTE_DATABASE_HOST=db
AIRBYTE_DATABASE_PORT=5432
AIRBYTE_DATABASE_USER=airbyte
AIRBYTE_DATABASE_PASSWORD=airbyte
AIRBYTE_DATABASE_NAME=airbyte

# =============================================================================
# LOGGING & MONITORING
# =============================================================================
LOG_LEVEL=INFO
LOG_FORMAT=json
ADMIN_EMAILS=["<EMAIL>", "<EMAIL>"]
