"""
Authentication models for ProductVideo platform.
Includes User, OAuth providers, and session management.
"""

from datetime import datetime, <PERSON><PERSON><PERSON>
from typing import Optional, List
from enum import Enum

from sqlalchemy import Column, Integer, String, Boolean, DateTime, Text, ForeignKey, UniqueConstraint, Float, JSON
from sqlalchemy.orm import relationship
from sqlalchemy.dialects.postgresql import UUID
import uuid

from core.db.database import Base


class UserRole(str, Enum):
    """User roles in the system."""
    ADMIN = "admin"
    USER = "user"
    MERCHANT = "merchant"


class OAuthProvider(str, Enum):
    """Supported OAuth providers."""
    GOOGLE = "google"
    GITHUB = "github"
    SHOPIFY = "shopify"


class User(Base):
    """User model with support for multiple authentication methods."""
    __tablename__ = "users"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)
    
    # Basic user information
    email = Column(String(255), unique=True, index=True, nullable=False)
    username = Column(String(100), unique=True, index=True, nullable=True)
    first_name = Column(String(100), nullable=True)
    last_name = Column(String(100), nullable=True)
    avatar_url = Column(String(500), nullable=True)
    
    # Authentication
    password_hash = Column(String(255), nullable=True)  # Nullable for OAuth-only users
    is_active = Column(Boolean, default=True, nullable=False)
    is_verified = Column(Boolean, default=False, nullable=False)
    role = Column(String(20), default=UserRole.USER.value, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_login_at = Column(DateTime, nullable=True)
    
    # Email verification
    email_verification_token = Column(String(255), nullable=True)
    email_verification_expires = Column(DateTime, nullable=True)
    
    # Password reset
    password_reset_token = Column(String(255), nullable=True)
    password_reset_expires = Column(DateTime, nullable=True)
    
    # Relationships
    oauth_accounts = relationship("OAuthAccount", back_populates="user", cascade="all, delete-orphan")
    sessions = relationship("UserSession", back_populates="user", cascade="all, delete-orphan")
    tenants = relationship("Tenant", back_populates="owner")
    stores = relationship("Store", back_populates="owner")
    
    @property
    def full_name(self) -> str:
        """Get user's full name."""
        if self.first_name and self.last_name:
            return f"{self.first_name} {self.last_name}"
        elif self.first_name:
            return self.first_name
        elif self.last_name:
            return self.last_name
        else:
            return self.username or self.email.split('@')[0]
    
    @property
    def display_name(self) -> str:
        """Get user's display name."""
        return self.username or self.full_name
    
    def has_password(self) -> bool:
        """Check if user has a password set."""
        return self.password_hash is not None
    
    def has_oauth_provider(self, provider: OAuthProvider) -> bool:
        """Check if user has connected a specific OAuth provider."""
        return any(account.provider == provider.value for account in self.oauth_accounts)


class OAuthAccount(Base):
    """OAuth account linking for social login."""
    __tablename__ = "oauth_accounts"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # OAuth provider information
    provider = Column(String(50), nullable=False)  # google, github, shopify
    provider_user_id = Column(String(255), nullable=False)  # ID from the OAuth provider
    provider_username = Column(String(255), nullable=True)  # Username from provider
    provider_email = Column(String(255), nullable=True)  # Email from provider
    
    # OAuth tokens
    access_token = Column(Text, nullable=True)
    refresh_token = Column(Text, nullable=True)
    token_expires_at = Column(DateTime, nullable=True)
    
    # Provider-specific data
    provider_data = Column(Text, nullable=True)  # JSON string of additional data
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)
    last_used_at = Column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="oauth_accounts")
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('provider', 'provider_user_id', name='unique_provider_user'),
    )


class UserSession(Base):
    """User session management for JWT tokens."""
    __tablename__ = "user_sessions"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    
    # Session information
    session_token = Column(String(255), unique=True, index=True, nullable=False)
    refresh_token = Column(String(255), unique=True, index=True, nullable=False)
    
    # Device/client information
    user_agent = Column(String(500), nullable=True)
    ip_address = Column(String(45), nullable=True)  # IPv6 compatible
    device_name = Column(String(255), nullable=True)
    
    # Session status
    is_active = Column(Boolean, default=True)
    expires_at = Column(DateTime, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    last_used_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="sessions")
    
    @property
    def is_expired(self) -> bool:
        """Check if session is expired."""
        return datetime.utcnow() > self.expires_at
    
    def extend_session(self, days: int = 30):
        """Extend session expiration."""
        self.expires_at = datetime.utcnow() + timedelta(days=days)
        self.last_used_at = datetime.utcnow()


class EmailVerification(Base):
    """Email verification tokens."""
    __tablename__ = "email_verifications"

    id = Column(Integer, primary_key=True, index=True)
    email = Column(String(255), nullable=False, index=True)
    token = Column(String(255), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_used = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    @property
    def is_expired(self) -> bool:
        """Check if verification token is expired."""
        return datetime.utcnow() > self.expires_at


class PasswordReset(Base):
    """Password reset tokens."""
    __tablename__ = "password_resets"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("users.id"), nullable=False)
    token = Column(String(255), unique=True, index=True, nullable=False)
    expires_at = Column(DateTime, nullable=False)
    is_used = Column(Boolean, default=False)
    created_at = Column(DateTime, default=datetime.utcnow)
    
    # Relationships
    user = relationship("User")
    
    @property
    def is_expired(self) -> bool:
        """Check if reset token is expired."""
        return datetime.utcnow() > self.expires_at


# Update Tenant model to reference User
class Tenant(Base):
    """Tenant model for multi-tenancy support."""
    __tablename__ = "tenants"

    id = Column(Integer, primary_key=True, index=True)
    uuid = Column(UUID(as_uuid=True), default=uuid.uuid4, unique=True, index=True)

    # Tenant information
    name = Column(String(255), nullable=False)
    slug = Column(String(100), unique=True, index=True, nullable=False)
    description = Column(Text, nullable=True)

    # Owner relationship
    owner_id = Column(Integer, ForeignKey("users.id"), nullable=False)

    # Tenant settings
    is_active = Column(Boolean, default=True)
    plan = Column(String(50), default="free")  # free, pro, enterprise

    # Storage limits (in GB)
    storage_limit_gb = Column(Float, default=5.0)  # Default 5GB
    storage_used_gb = Column(Float, default=0.0)

    # Billing fields
    stripe_customer_id = Column(String(255), nullable=True)
    trial_ends_at = Column(DateTime, nullable=True)
    shop_domain = Column(String(255), nullable=True)
    shop_id = Column(String(50), nullable=True)
    billing_email = Column(String(255), nullable=True)
    settings = Column(JSON, nullable=True, default=dict)

    # Timestamps
    created_at = Column(DateTime, default=datetime.utcnow)
    updated_at = Column(DateTime, default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    owner = relationship("User", back_populates="tenants")
    stores = relationship("Store", back_populates="tenant")
    media_jobs = relationship("MediaJob", back_populates="tenant")
    subscriptions = relationship("Subscription", back_populates="tenant")
    usage_records = relationship("BillingUsage", back_populates="tenant")
    generation_batches = relationship("GenerationBatch", back_populates="workspace")
    generated_assets = relationship("GeneratedAsset", back_populates="workspace")
    scraped_documents = relationship("ScrapedDocument", back_populates="workspace")
