from datetime import datetime
from typing import Optional, List, Any
from pydantic import BaseModel

from core.schemas.base_schemas import BaseCreateSchema, BaseResponseSchema, BaseUpdateSchema


class ProductVariantBase(BaseModel):
    """Base product variant schema - matches the actual ProductVariant model."""

    external_id: str
    title: Optional[str] = None
    sku: Optional[str] = None
    barcode: Optional[str] = None
    price: Optional[float] = None
    compare_at_price: Optional[float] = None
    cost: Optional[float] = None
    weight: Optional[float] = None
    weight_unit: str = "kg"
    quantity: int = 0
    inventory_policy: Optional[str] = "deny"
    inventory_item_id: Optional[str] = None
    option1: Optional[str] = None
    option2: Optional[str] = None
    option3: Optional[str] = None
    taxable: bool = True
    requires_shipping: bool = True
    fulfillment_service: Optional[str] = "manual"
    available_for_sale: bool = True
    full_json: Optional[str] = None  # Complete variant data
    source_updated_at: Optional[datetime] = None


class ProductVariantResponse(BaseResponseSchema, ProductVariantBase):
    """Product variant response schema."""

    product_id: int


class ProductImageBase(BaseModel):
    """Base product image schema."""

    external_id: str
    src: str
    alt: Optional[str] = None
    width: Optional[int] = None
    height: Optional[int] = None
    position: int = 0


class ProductImageResponse(BaseResponseSchema, ProductImageBase):
    """Product image response schema."""

    product_id: int
    variant_id: Optional[int] = None


class ProductBase(BaseModel):
    """Base product schema - matches the actual Product model."""

    external_id: str
    title: str
    handle: Optional[str] = None
    vendor: Optional[str] = None
    product_type: Optional[str] = None
    status: str = "active"
    published: bool = True
    description: Optional[str] = None
    tags: Optional[str] = None  # JSON string
    options: Optional[str] = None  # JSON string
    seo: Optional[str] = None  # JSON string
    metafields: Optional[str] = None  # JSON string - Custom metadata fields
    collections: Optional[str] = None  # JSON string
    full_json: Optional[str] = None  # Complete raw data from platform
    featured_media: Optional[str] = None  # JSON: Featured media from Shopify (can be image or video)
    published_at: Optional[datetime] = None
    source_updated_at: Optional[datetime] = None

    class Config:
        from_attributes = True


class ProductCreate(BaseCreateSchema, ProductBase):
    """Schema for creating a product."""

    store_id: int
    variants: Optional[List[ProductVariantBase]] = None


class ProductUpdate(BaseUpdateSchema):
    """Schema for updating a product."""

    title: Optional[str] = None
    handle: Optional[str] = None
    vendor: Optional[str] = None
    product_type: Optional[str] = None
    status: Optional[str] = None
    published: Optional[bool] = None
    description: Optional[str] = None
    tags: Optional[str] = None
    options: Optional[str] = None
    seo: Optional[str] = None
    metafields: Optional[str] = None
    collections: Optional[str] = None
    featured_media: Optional[str] = None
    published_at: Optional[datetime] = None


class ProductListResponse(BaseResponseSchema, ProductBase):
    """Product list response schema (with variants for price display)."""

    store_id: int
    variant_count: Optional[int] = 0
    image_count: Optional[int] = 0
    images: Optional[List[ProductImageResponse]] = None
    variants: Optional[List[ProductVariantResponse]] = None


class ProductResponse(BaseResponseSchema, ProductBase):
    """Product response schema with full details."""

    store_id: int
    variants: Optional[List[ProductVariantResponse]] = None
    images: Optional[List[ProductImageResponse]] = None


class PaginatedProductResponse(BaseModel):
    """Paginated product list response."""

    items: List[ProductListResponse]
    total: int
    page: int
    limit: int
    total_pages: int

    class Config:
        from_attributes = True