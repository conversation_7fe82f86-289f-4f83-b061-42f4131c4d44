"""
Products API Router
"""

import logging
from typing import List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.ext.asyncio import AsyncSession

from core.db.database import get_db
from modules.auth.router import get_current_user
from modules.auth.models import User
from modules.products.models import Product, ProductVariant, ProductImage
from modules.products.schemas import (
    ProductCreate,
    ProductListResponse,
    ProductResponse,
    ProductUpdate,
    PaginatedProductResponse
)
from modules.products.service import product_service

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/", response_model=PaginatedProductResponse)
async def get_products(
    page: int = 1,
    limit: int = 50,
    search: str = None,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get all products for the current user's stores."""
    try:
        logger.info(f"Products API called with page={page}, limit={limit}" + (f", search='{search}'" if search else ""))

        # Get user's stores
        from modules.stores.models import Store
        from sqlalchemy import select

        stores_result = await db.execute(
            select(Store).filter(Store.owner_id == current_user.id)
        )
        user_stores = stores_result.scalars().all()

        logger.info(f"User {current_user.id} has {len(user_stores)} stores")
        for store in user_stores:
            logger.info(f"Store: {store.id} - {store.shop_name or 'Unnamed Store'}")

        if not user_stores:
            logger.info("No stores found for user")
            return PaginatedProductResponse(
                items=[],
                total=0,
                page=page,
                limit=limit,
                total_pages=0
            )

        store_ids = [store.id for store in user_stores]

        # Get ALL products for all user's stores with their relationships loaded
        from sqlalchemy.orm import selectinload
        from sqlalchemy import select, or_, and_

        all_products = []
        for store_id in store_ids:
            # Build the base query
            query = select(Product).options(
                selectinload(Product.variants),
                selectinload(Product.images)
            ).filter(Product.store_id == store_id)

            # Add search filter if search query is provided
            if search:
                search_filter = f"%{search.lower()}%"
                query = query.filter(
                    or_(
                        Product.title.ilike(search_filter),
                        Product.description.ilike(search_filter),
                        Product.vendor.ilike(search_filter),
                        Product.product_type.ilike(search_filter),
                        Product.tags.ilike(search_filter)
                    )
                )
                logger.debug(f"Applying search filter: {search}")

            query = query.order_by(Product.updated_at.desc())

            # Execute the query
            products_result = await db.execute(query)
            store_products = products_result.scalars().all()

            logger.debug(f"Store {store_id} has {len(store_products)} products" + (f" matching '{search}'" if search else ""))

            # Add counts as attributes
            for product in store_products:
                product.variant_count = len(product.variants) if product.variants else 0
                product.image_count = len(product.images) if product.images else 0

                # Log if no images found (for debugging)
                if product.image_count == 0:
                    logger.debug(f"Product {product.title}: no images found")

            all_products.extend(store_products)

        # Calculate total count
        total_products = len(all_products)
        logger.info(f"Total products found: {total_products}")

        # Apply pagination to the combined results
        start_idx = (page - 1) * limit
        end_idx = start_idx + limit
        paginated_products = all_products[start_idx:end_idx]

        # Calculate total pages
        total_pages = (total_products + limit - 1) // limit  # Ceiling division

        logger.info(f"Returning page {page} with {len(paginated_products)} products out of {total_products} total")

        response = PaginatedProductResponse(
            items=paginated_products,
            total=total_products,
            page=page,
            limit=limit,
            total_pages=total_pages
        )

        logger.info(f"Response total field: {response.total}")
        return response

    except Exception as e:
        logger.error(f"Error getting products: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/", response_model=ProductResponse)
async def create_product(
    product: ProductCreate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Create a new product."""
    try:
        # Validate store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        db_product = await product_service.create_product_with_variants(db, product)
        return db_product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error creating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/{product_id}", response_model=ProductResponse)
async def get_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get a specific product."""
    try:
        logger.info(f"Getting product {product_id} for user {current_user.id}")

        product = await product_service.get_product_with_full_details(db, product_id)

        if not product:
            logger.warning(f"Product {product_id} not found")
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            logger.warning(f"User {current_user.id} does not have access to product {product_id} (store {product.store_id})")
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        # Debug metafields
        logger.info(f"Product {product_id} metafields: {product.metafields}")
        logger.info(f"Product {product_id} metafields type: {type(product.metafields)}")

        return product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.put("/{product_id}", response_model=ProductResponse)
async def update_product(
    product_id: int,
    product_update: ProductUpdate,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Update a product."""
    try:
        # Get existing product
        product = await product_service.get(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        updated_product = await product_service.update(db, db_obj=product, obj_in=product_update)
        return updated_product

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error updating product: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.delete("/{product_id}")
async def delete_product(
    product_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Delete a product."""
    try:
        # Get existing product
        product = await product_service.get(db, product_id)
        if not product:
            raise HTTPException(status_code=404, detail="Product not found")

        # Check store ownership
        from modules.stores.models import Store
        from sqlalchemy import select

        store_result = await db.execute(
            select(Store).filter(
                Store.id == product.store_id,
                Store.owner_id == current_user.id
            )
        )
        store = store_result.scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=403,
                detail="Access denied: product does not belong to user's store"
            )

        await product_service.remove(db, id=product_id)
        return {"message": "Product deleted successfully"}

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error deleting product: {e}")
        raise HTTPException(status_code=500, detail=str(e))

