from typing import List, Optional
from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from core.services.base_service import BaseService
from modules.products.models import Product, ProductVariant
from modules.products.schemas import ProductCreate, ProductUpdate


class ProductService(BaseService[Product, ProductCreate, ProductUpdate]):
    """Service for product operations."""

    def __init__(self):
        super().__init__(Product)

    async def get_by_store(self, db: AsyncSession, store_id: int) -> List[Product]:
        """Get all products for a specific store."""
        result = await db.execute(
            select(Product)
            .filter(Product.store_id == store_id)
            .order_by(Product.updated_at.desc())
        )
        return result.scalars().all()

    async def get_by_store_paginated(self, db: AsyncSession, store_id: int, page: int = 1, limit: int = 50) -> List[Product]:
        """Get paginated products for a specific store."""
        offset = (page - 1) * limit
        result = await db.execute(
            select(Product)
            .filter(Product.store_id == store_id)
            .order_by(Product.updated_at.desc())
            .offset(offset)
            .limit(limit)
        )
        return result.scalars().all()

    async def get_by_external_id(self, db: AsyncSession, external_id: str) -> Optional[Product]:
        """Get product by external ID (Shopify product ID)."""
        result = await db.execute(
            select(Product).filter(Product.external_id == external_id)
        )
        return result.scalar_one_or_none()

    async def create_product_with_variants(
        self,
        db: AsyncSession,
        product_create: ProductCreate
    ) -> Product:
        """Create a product with its variants."""
        # Create the product
        product_data = product_create.model_dump(exclude={'variants'})
        db_product = Product(**product_data)
        db.add(db_product)
        await db.flush()  # Get the product ID

        # Create variants if provided
        if product_create.variants:
            for variant_data in product_create.variants:
                variant_dict = variant_data.model_dump()
                variant_dict['product_id'] = db_product.id
                db_variant = ProductVariant(**variant_dict)
                db.add(db_variant)

        await db.commit()
        await db.refresh(db_product)
        return db_product

    async def get_product_with_variants(
        self,
        db: AsyncSession,
        product_id: int
    ) -> Optional[Product]:
        """Get product with its variants."""
        from sqlalchemy.orm import selectinload

        result = await db.execute(
            select(Product)
            .options(selectinload(Product.variants))
            .filter(Product.id == product_id)
        )
        return result.scalar_one_or_none()

    async def get_product_with_full_details(
        self,
        db: AsyncSession,
        product_id: int
    ) -> Optional[Product]:
        """Get product with variants and images."""
        from sqlalchemy.orm import selectinload

        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.id == product_id)
        )
        return result.scalar_one_or_none()

    async def get_by_store_with_counts(
        self,
        db: AsyncSession,
        store_id: int,
        page: int = 1,
        limit: int = 50
    ) -> List[Product]:
        """Get products for a store with variant and image counts."""
        from sqlalchemy import func
        from sqlalchemy.orm import selectinload

        offset = (page - 1) * limit

        # Get products with counts
        result = await db.execute(
            select(Product)
            .options(
                selectinload(Product.variants),
                selectinload(Product.images)
            )
            .filter(Product.store_id == store_id)
            .order_by(Product.updated_at.desc())
            .offset(offset)
            .limit(limit)
        )

        products = result.scalars().all()

        # Add counts as attributes for the response
        for product in products:
            product.variant_count = len(product.variants) if product.variants else 0
            product.image_count = len(product.images) if product.images else 0

        return products


# Create service instance
product_service = ProductService()