"""
Media Generation API Router - Complete ProductMedia implementation
"""

import logging
from datetime import datetime
from typing import Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException, Query, status, BackgroundTasks
from pydantic import BaseModel
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func

from core.config import get_settings
from core.db.database import get_db

from modules.auth.models import User, Tenant
from modules.auth.router import get_current_user
from core.services.queue_service import celery_service as job_queue_service, TaskPriority
from .models import (
    MediaJob, MediaVariant, Template, Voice, MediaJobStatus, MediaVariantStatus,
    VideoProvider, ImageProvider, VoiceProvider, PushStatus
)
from .schemas import (
    MediaGenerateRequest, MediaGenerateResponse, MediaJobStatusResponse,
    MediaRegenerateRequest, MediaPushRequest, MediaPushResponse, MediaJobListResponse,
    MediaVariantListResponse, PushStatusResponse, MediaGenerationRequest,
    MediaVariantInfo, MediaJobInfo, MediaLimitsResponse,
    BatchGenerationRequest, BatchGenerationResponse, BatchStatusResponse,
    BatchDetailsResponse, AssetInfo, AssetsResponse, UsageStatsResponse
)
from .service import VoiceGender, VoiceStyle, TTSRequest
from .service import media_service
from modules.stores.models import Store

logger = logging.getLogger(__name__)
router = APIRouter()
settings = get_settings()


class ScriptGenerationRequest(BaseModel):
    """Request model for script generation."""
    product_title: str
    product_description: Optional[str] = None
    key_features: Optional[List[str]] = None
    price: Optional[float] = None
    style: str = "promotional"  # promotional, informational, testimonial


class VoiceTestRequest(BaseModel):
    """Request model for voice testing."""
    voice_id: str
    text: str = "Hello! This is a test of this voice. How does it sound?"
    speed: float = 1.0
    pitch: float = 1.0


@router.post("/generate", response_model=MediaGenerateResponse)
async def generate_videos(
    request: MediaGenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Generate 4 video variants for selected products.
    
    Body: {shopId, productIds[], templateId?, voiceId?, aspectRatio?, locale?}
    Returns: job IDs per product-variant
    """
    try:
        # Validate shop ownership
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if hasattr(request, 'shop_id') and request.shop_id:
            if request.shop_id not in store_ids:
                raise HTTPException(
                    status_code=403,
                    detail="Access denied: shop does not belong to user"
                )

        # Create video generation jobs
        jobs = await media_service.create_generation_jobs(
            db=db,
            user_id=current_user.id,
            request=request
        )
        
        # Queue background generation tasks using BullMQ
        for job in jobs:
            background_tasks.add_task(
                media_service.process_generation_job,
                db,
                job.id
            )
        
        return MediaGenerateResponse(
            jobs=[{
                "product_id": job.product_id,
                "job_id": job.id,
                "status": job.status.value
            } for job in jobs]
        )
        
    except Exception as e:
        logger.error(f"Error generating videos: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs/{job_id}", response_model=MediaJobStatusResponse)
async def get_job_status(
    job_id: int,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get job status, progress, and variant IDs.
    """
    try:
        job = await media_service.get_job_with_variants(db, job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")
        
        # Add ownership validation
        # Check if job belongs to user's tenant
        if hasattr(job, 'tenant_id') and job.tenant_id != current_user.id:
            raise HTTPException(
                status_code=403,
                detail="Access denied: job does not belong to user"
            )

        return MediaJobStatusResponse(
            job_id=job.id,
            status=job.status.value,
            progress=job.progress_percentage,
            variants=[{
                "variant_id": variant.id,
                "variant_name": variant.variant_name,
                "status": variant.status.value,
                "video_url": variant.video_url,
                "image_url": variant.image_url,
                "voice_url": variant.voice_url,
                "thumbnail_url": variant.thumbnail_url,
                "duration": variant.duration_seconds
            } for variant in job.variants] if hasattr(job, 'variants') else []
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting job status: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/regenerate")
async def regenerate_variant(
    request: MediaRegenerateRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Regenerate a specific media variant.
    """
    try:
        # Get the original job and variant
        job = await media_service.get_job_with_variants(db, request.job_id)
        if not job:
            raise HTTPException(status_code=404, detail="Job not found")

        # Find the specific variant
        variant = None
        for v in job.variants:
            if str(v.id) == request.variant_id:
                variant = v
                break

        if not variant:
            raise HTTPException(status_code=404, detail="Variant not found")

        # Check ownership
        if hasattr(job, 'tenant_id') and job.tenant_id != current_user.id:
            raise HTTPException(status_code=403, detail="Access denied: job does not belong to user")

        # Reset variant status
        variant.status = MediaVariantStatus.GENERATING
        variant.error_message = None
        variant.updated_at = datetime.now()

        # Update job status if needed
        if job.status == MediaJobStatus.COMPLETED:
            job.status = MediaJobStatus.PROCESSING

        await db.commit()

        # Re-queue the job with regeneration parameters
        background_tasks.add_task(
            media_service.process_generation_job,
            db,
            job.id
        )

        logger.info(f"Regenerating variant {request.variant_id} for job {request.job_id}")
        return {
            "message": "Regeneration queued",
            "job_id": request.job_id,
            "variant_id": request.variant_id,
            "status": "queued"
        }
    except Exception as e:
        logger.error(f"Error regenerating variant: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.post("/push", response_model=MediaPushResponse)
async def push_to_platform(
    request: MediaPushRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Push selected video variant to connected store's product media.

    Body: {shopId, productId, variantId, publishTargets, publishOptions}
    """
    try:
        # Validate store ownership and permissions
        # Get user's stores
        user_stores_result = await db.execute(select(Store).filter(Store.owner_id == current_user.id))
        user_stores = user_stores_result.scalars().all()
        if not user_stores:
            raise HTTPException(
                status_code=404,
                detail="No stores found for user"
            )

        store_ids = [store.id for store in user_stores]

        # Check if the requested shop_id belongs to user's stores
        if request.shop_id not in store_ids:
            raise HTTPException(
                status_code=403,
                detail="Access denied: store does not belong to user"
            )

        # Find the store to determine platform
        store = await db.execute(select(Store).filter(
            Store.id == request.shop_id,
            Store.owner_id == current_user.id
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=404,
                detail="Store not found"
            )

        # Queue push task
        background_tasks.add_task(
            media_service.push_to_platform,
            request.shop_id,
            request.product_id,
            request.variant_id,
            request.publish_options
        )

        platform_name = store.platform.title()
        return MediaPushResponse(
            push_id=f"push_{request.variant_id}_{request.product_id}",
            status="queued",
            message=f"Push to {platform_name} queued"
        )

    except Exception as e:
        logger.error(f"Error pushing to platform: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/templates")
async def get_templates(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get available video templates."""
    try:
        templates = await media_service.get_templates(db)
        return {"templates": templates}
    except Exception as e:
        logger.error(f"Error getting templates: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/voices")
async def get_voices(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """Get available voices."""
    try:
        voices = await media_service.get_voices(db)
        return {"voices": voices}
    except Exception as e:
        logger.error(f"Error getting voices: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/jobs", response_model=MediaJobListResponse)
async def list_jobs(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    product_id: Optional[str] = Query(None),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media generation jobs for the current user.

    Supports filtering by status and product ID, with pagination.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Build query
        query = select(MediaJob).filter(MediaJob.tenant_id == tenant.id)

        # Apply filters
        if status_filter:
            try:
                status_enum = MediaJobStatus(status_filter)
                query = query.filter(MediaJob.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )

        if product_id:
            query = query.filter(MediaJob.product_id == product_id)

        # Get total count
        total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

        # Apply pagination
        offset = (page - 1) * per_page
        jobs = (await db.execute(query.order_by(MediaJob.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

        # Convert to response models
        job_responses = []
        for job in jobs:
            variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
            variant_responses = [MediaVariantInfo.from_orm(v) for v in variants]

            job_response = MediaJobInfo.from_orm(job)
            job_response.variants = variant_responses
            job_responses.append(job_response)

        return MediaJobListResponse(
            jobs=job_responses,
            total=total,
            page=page,
            per_page=per_page
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list jobs: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list jobs: {str(e)}"
        )


@router.get("/variants", response_model=MediaVariantListResponse)
async def list_variants(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    status_filter: Optional[str] = Query(None),
    job_id: Optional[str] = Query(None),
    favorites_only: bool = Query(False),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List media variants for the current user.

    Supports filtering by status, job ID, and favorites, with pagination.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Build query
        query = select(MediaVariant).filter(MediaVariant.tenant_id == tenant.id)

        # Apply filters
        if status_filter:
            try:
                status_enum = MediaVariantStatus(status_filter)
                query = query.filter(MediaVariant.status == status_enum)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid status filter: {status_filter}"
                )

        if job_id:
            job = await db.execute(select(MediaJob).filter(MediaJob.external_job_id == job_id, MediaJob.tenant_id == tenant.id)).scalar_one_or_none()
            if job:
                query = query.filter(MediaVariant.job_id == job.id)
            else:
                # Job not found, return empty results
                return MediaVariantListResponse(variants=[], total=0, page=page, per_page=per_page)

        if favorites_only:
            query = query.filter(MediaVariant.is_favorite == True)

        # Get total count
        total = (await db.execute(select(func.count()).select_from(query.subquery()))).scalar_one()

        # Apply pagination
        offset = (page - 1) * per_page
        variants = (await db.execute(query.order_by(MediaVariant.created_at.desc()).offset(offset).limit(per_page))).scalars().all()

        # Convert to response models
        variant_responses = [MediaVariantInfo.from_orm(v) for v in variants]

        return MediaVariantListResponse(
            variants=variant_responses,
            total=total,
            page=page,
            per_page=per_page
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list variants: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list variants: {str(e)}"
        )


@router.patch("/variants/{variant_id}/favorite")
async def toggle_variant_favorite(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Toggle the favorite status of a media variant.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(MediaVariant.external_variant_id == variant_id, MediaVariant.tenant_id == tenant.id)).scalar_one_or_none()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant not found"
            )

        # Toggle favorite status
        variant.is_favorite = not variant.is_favorite
        await db.commit()

        return {
            "success": True,
            "variant_id": variant_id,
            "is_favorite": variant.is_favorite,
            "message": f"Variant {'added to' if variant.is_favorite else 'removed from'} favorites"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to toggle favorite for variant {variant_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to toggle favorite: {str(e)}"
        )


@router.patch("/variants/{variant_id}/rating")
async def rate_variant(
    variant_id: str,
    rating: int = Query(..., ge=1, le=5),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Rate a media variant (1-5 stars).
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(MediaVariant.external_variant_id == variant_id, MediaVariant.tenant_id == tenant.id)).scalar_one_or_none()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Variant not found"
            )

        # Update rating
        variant.user_rating = rating
        await db.commit()

        return {
            "success": True,
            "variant_id": variant_id,
            "rating": rating,
            "message": f"Variant rated {rating} stars"
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to rate variant {variant_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to rate variant: {str(e)}"
        )


@router.delete("/jobs/{job_id}")
async def cancel_job(
    job_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Cancel a media generation job.

    Cancels the job with the AI provider and updates the local status.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the job in database
        job = await db.execute(select(MediaJob).filter(MediaJob.external_job_id == job_id, MediaJob.tenant_id == tenant.id)).scalar_one_or_none()

        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Job not found"
            )

        # Cancel the job in queue if it's still pending
        queue_cancelled = await job_queue_service.cancel_job(job_id)

        # Update job status in database
        if job.status in [MediaJobStatus.PENDING, MediaJobStatus.PROCESSING]:
            job.status = MediaJobStatus.CANCELLED
            job.completed_at = datetime.now()

            # Mark variants as failed
            variants = (await db.execute(select(MediaVariant).filter(MediaVariant.job_id == job.id))).scalars().all()
            for variant in variants:
                variant.status = MediaVariantStatus.FAILED
                variant.updated_at = datetime.now()

            await db.commit()

        return {
            "success": True,
            "job_id": job_id,
            "message": "Job cancelled successfully",
            "queue_cancelled": queue_cancelled
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to cancel job {job_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to cancel job: {str(e)}"
        )


@router.get("/processing/stats")
async def get_processing_stats(
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get media processing statistics.

    Returns information about current processing status and provider info.
    """
    try:
        # Add admin role check
        # For now, check if user email contains 'admin' or is in a hardcoded list
        admin_emails = getattr(settings, 'ADMIN_EMAILS', ['<EMAIL>', '<EMAIL>'])
        if current_user.email not in admin_emails and 'admin' not in current_user.email.lower():
            raise HTTPException(
                status_code=403,
                detail="Admin access required"
            )

        queue_stats = await job_queue_service.get_queue_stats()

        # Get provider info from the new provider system
        available_providers = await media_service.get_available_providers()

        provider_info = {
            "available_providers": available_providers,
            "default_providers": {
                "image": media_service._get_provider_for_media_type("image"),
                "video": media_service._get_provider_for_media_type("video"),
                "voice": media_service._get_provider_for_media_type("voice")
            },
            "supported_media_types": ["image", "video", "voice"],
            "max_variants_per_job": 4,
            "supported_aspect_ratios": ["16:9", "9:16", "1:1", "4:5", "3:4"],
            "supported_locales": ["en", "es", "fr", "de", "it"]
        }

        return {
            "queue_stats": queue_stats,
            "provider_info": provider_info,
            "timestamp": datetime.utcnow().isoformat()
        }

    except Exception as e:
        logger.exception(f"Failed to get processing stats: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get processing stats: {str(e)}"
        )


@router.get("/products/{product_id}")
async def get_product_for_media(
    product_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get product data from connected store for media generation.

    Returns product title, description, and images needed for AI generation.
    """
    try:
        # Find user's active store (any platform)
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.is_active == True
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active store found"
            )

        # Delegate to platform-specific plugin
        if store.platform == "shopify":
            # Use plugin registry for dynamic service access
            try:
                from plugins import has_platform
                if not has_platform("shopify"):
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Shopify plugin not available"
                    )

                # Import Shopify service dynamically
                import importlib
                shopify_module = importlib.import_module("plugins.shopify.shopify_service")
                ShopifyGraphQLService = shopify_module.ShopifyGraphQLService
                shopify_service = ShopifyGraphQLService(
                    shop_domain=store.shop_domain,
                    admin_access_token=store.admin_access_token
                )
            except ImportError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Shopify plugin not available: {str(e)}"
                )

            # Get product details
            products_result = await shopify_service.get_products(limit=1)
            products = products_result.get("edges", [])

            # Find the specific product
            product_data = None
            for edge in products:
                node = edge.get("node", {})
                if str(node.get("legacyResourceId")) == product_id:
                    product_data = node
                    break

            if not product_data:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Product not found"
                )

            # Extract relevant data for media generation
            return {
                "product_id": product_id,
                "title": product_data.get("title", ""),
                "description": product_data.get("description", ""),
                "images": [
                    {
                        "url": img.get("url"),
                        "alt_text": img.get("altText")
                    }
                    for img in product_data.get("images", {}).get("edges", [])
                ],
                "variants": [
                    {
                        "id": variant.get("node", {}).get("id"),
                        "title": variant.get("node", {}).get("title"),
                        "price": variant.get("node", {}).get("price")
                    }
                    for variant in product_data.get("variants", {}).get("edges", [])
                ]
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Platform {store.platform} not yet supported for product data"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get product data: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get product data: {str(e)}"
        )


@router.get("/limits/{product_id}", response_model=MediaLimitsResponse)
async def get_media_limits(
    product_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get media limits for a product from connected store.

    Returns current video count and remaining capacity.
    """
    try:
        # Find user's active store (any platform)
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.is_active == True
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="No active store found"
            )

        # Delegate to platform-specific plugin
        if store.platform == "shopify":
            # Get media limits using Shopify GraphQL service
            try:
                from plugins import has_platform
                if not has_platform("shopify"):
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Shopify plugin not available"
                    )

                # Import Shopify service dynamically
                import importlib
                shopify_module = importlib.import_module("plugins.shopify.shopify_service")
                ShopifyGraphQLService = shopify_module.ShopifyGraphQLService
                shopify_service = ShopifyGraphQLService(
                    shop_domain=store.shop_domain,
                    admin_access_token=store.admin_access_token
                )
            except ImportError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Shopify plugin not available: {str(e)}"
                )

            # Get product media to count existing videos
            media_list = await shopify_service.get_product_media(product_id)
            current_count = len([m for m in media_list if m.get('mediaContentType') == 'VIDEO'])

            # Shopify allows up to 100 media items per product
            max_count = 100
            remaining = max_count - current_count
            can_add_more = remaining > 0

            return MediaLimitsResponse(
                current_count=current_count,
                max_count=max_count,
                remaining=remaining,
                can_add_more=can_add_more
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Platform {store.platform} not yet supported for media limits"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get media limits: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get media limits: {str(e)}"
        )


@router.get("/status/{variant_id}", response_model=PushStatusResponse)
async def get_push_status(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get push status for a media variant.
    
    Returns current push status and Shopify media information.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()
        
        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )
        
        return PushStatusResponse(
            variant_id=variant_id,
            status=variant.push_status,
            media_id=variant.media_id,
            pushed_at=variant.pushed_at.isoformat() if variant.pushed_at else None,
            error_message=variant.push_error_message
        )
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get push status: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get push status: {str(e)}"
        )


@router.post("/retry/{variant_id}")
async def retry_push(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Retry pushing a failed media variant to Shopify.
    
    Requeues the push job for processing.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )
        
        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()
        
        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )
        
        if variant.push_status != PushStatus.FAILED:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Can only retry failed pushes"
            )
        
        # Get the job and store
        job = await db.execute(select(MediaJob).filter(MediaJob.id == variant.job_id)).scalar_one_or_none()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Associated job not found"
            )
        
        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.is_active == True
        )).scalar_one_or_none()
        
        if not store or not store.admin_access_token:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or not connected"
            )
        
        # Reset status and queue retry
        variant.push_status = PushStatus.PUSHING
        variant.push_error_message = None
        
        job_id = await job_queue_service.enqueue_job(
            job_type="media_push",
            payload={
                "store_id": store.id,
                "variant_id": variant.external_variant_id,
                "product_id": job.product_id,
                "video_url": variant.video_url,
                "alt_text": variant.alt_text,
                "position": None,
                "replace_existing": False
            },
            priority=JobPriority.HIGH,
            max_attempts=3,
            timeout_seconds=300,
            metadata={
                "tenant_id": tenant.id,
                "variant_id": variant.external_variant_id,
                "product_id": job.product_id,
                "retry": True
            }
        )
        
        await db.commit()
        
        return {
            "success": True,
            "message": "Push retry queued successfully",
            "job_id": job_id
        }
        
    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to retry push: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to retry push: {str(e)}"
        )


@router.delete("/remove/{variant_id}")
async def remove_from_platform(
    variant_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Remove a media from connected store's product media.

    Deletes the media from the platform and updates the variant status.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id)).scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Find the variant
        variant = await db.execute(select(MediaVariant).filter(
            MediaVariant.external_variant_id == variant_id,
            MediaVariant.tenant_id == tenant.id
        )).scalar_one_or_none()

        if not variant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Media variant not found"
            )

        if variant.push_status != PushStatus.COMPLETED or not variant.media_id:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Media is not currently in platform"
            )

        # Get the job and store
        job = await db.execute(select(MediaJob).filter(MediaJob.id == variant.job_id)).scalar_one_or_none()
        if not job:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Associated job not found"
            )

        store = await db.execute(select(Store).filter(
            Store.owner_id == current_user.id,
            Store.is_active == True
        )).scalar_one_or_none()

        if not store:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Store not found or not connected"
            )

        # Delegate to platform-specific plugin
        if store.platform == "shopify":
            # Delete from platform using Shopify GraphQL service
            try:
                from plugins import has_platform
                if not has_platform("shopify"):
                    raise HTTPException(
                        status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                        detail="Shopify plugin not available"
                    )

                # Import Shopify service dynamically
                import importlib
                shopify_module = importlib.import_module("plugins.shopify.shopify_service")
                ShopifyGraphQLService = shopify_module.ShopifyGraphQLService
                shopify_service = ShopifyGraphQLService(
                    shop_domain=store.shop_domain,
                    admin_access_token=store.admin_access_token
                )
            except ImportError as e:
                raise HTTPException(
                    status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                    detail=f"Shopify plugin not available: {str(e)}"
                )

            # Note: Shopify doesn't have a direct delete mutation for product media
            # This would need to be implemented using the productUpdateMedia mutation
            # For now, we'll mark as not implemented
            logger.warning("Media deletion from Shopify not yet implemented in GraphQL service")
            success = False
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Platform {store.platform} not yet supported for media removal"
            )

        if success:
            # Update variant status
            variant.push_status = PushStatus.PENDING
            variant.media_id = None
            variant.pushed_at = None
            await db.commit()

            return {
                "success": True,
                "message": "Media removed from platform successfully"
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to remove media from {store.platform.title()}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to remove media from platform: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove media: {str(e)}"
        )


# TTS-specific endpoints from voices module
@router.get("/voices/list")
async def list_tts_voices(
    language: Optional[str] = Query(None, description="Filter by language"),
    gender: Optional[VoiceGender] = Query(None, description="Filter by gender"),
    style: Optional[VoiceStyle] = Query(None, description="Filter by style"),
    include_premium: bool = Query(True, description="Include premium voices"),
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    List available TTS voices.

    Returns voices filtered by criteria and user's plan tier.
    """
    try:
        # Get user's tenant to check plan tier
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found. Please complete onboarding."
            )

        # Adjust premium filter based on plan
        if tenant.plan == "free":
            include_premium = False

        # Get voices
        voices = await media_service.get_available_voices_tts(
            language=language,
            gender=gender,
            style=style,
            include_premium=include_premium
        )

        return {
            "voices": [voice.dict() for voice in voices],
            "total_voices": len(voices),
            "filters": {
                "language": language,
                "gender": gender.value if gender else None,
                "style": style.value if style else None,
                "include_premium": include_premium
            },
            "user_plan": tenant.plan
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to list voices: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list voices: {str(e)}"
        )


@router.get("/voices/{voice_id}")
async def get_tts_voice(
    voice_id: str,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Get details for a specific voice.

    Returns voice information if user has access.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Get voice
        voice = await media_service.get_voice_by_id_tts(voice_id)

        if not voice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Voice not found"
            )

        # Check if user can access premium voices
        if voice.is_premium and tenant.plan == "free":
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Premium voice requires paid plan"
            )

        return voice.dict()

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to get voice {voice_id}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get voice: {str(e)}"
        )


@router.post("/voices/test")
async def test_tts_voice(
    request: VoiceTestRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Test a voice by generating a short audio sample.

    Returns audio URL for the test sample.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Get voice and check access
        voice = await media_service.get_voice_by_id_tts(request.voice_id)
        if not voice:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Voice not found"
            )

        if voice.is_premium and tenant.plan == "free":
            raise HTTPException(
                status_code=status.HTTP_402_PAYMENT_REQUIRED,
                detail="Premium voice requires paid plan"
            )

        # Generate test audio
        tts_request = TTSRequest(
            text=request.text,
            voice_id=request.voice_id,
            speed=request.speed,
            pitch=request.pitch
        )

        result = await media_service.synthesize_speech(tts_request)

        if result.success:
            return {
                "success": True,
                "audio_url": result.audio_url,
                "duration_seconds": result.duration_seconds,
                "voice_id": request.voice_id,
                "voice_name": voice.name
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Voice synthesis failed: {result.error_message}"
            )

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to test voice: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to test voice: {str(e)}"
        )


@router.post("/voices/generate-script")
async def generate_tts_script(
    request: ScriptGenerationRequest,
    current_user: User = Depends(get_current_user),
    db: AsyncSession = Depends(get_db)
):
    """
    Generate a script for product video narration.

    Returns AI-generated script based on product information.
    """
    try:
        # Get user's tenant
        tenant = db.query(Tenant).filter(Tenant.owner_id == current_user.id).first()
        if not tenant:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Tenant not found"
            )

        # Generate script
        script = media_service.generate_product_script(
            product_title=request.product_title,
            product_description=request.product_description,
            key_features=request.key_features,
            price=request.price,
            style=request.style
        )

        # Estimate speech duration (rough calculation)
        estimated_duration = len(script) * 0.08  # ~8 characters per second

        return {
            "script": script,
            "style": request.style,
            "estimated_duration_seconds": estimated_duration,
            "character_count": len(script),
            "word_count": len(script.split()),
            "product_title": request.product_title
        }

    except HTTPException:
        raise
    except Exception as e:
        logger.exception(f"Failed to generate script: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to generate script: {str(e)}"
        )


@router.get("/voices/styles/list")
async def list_tts_voice_styles(
    current_user: User = Depends(get_current_user)
):
    """
    List all available voice styles with descriptions.
    """
    try:
        styles = [
            {
                "style": VoiceStyle.CONVERSATIONAL.value,
                "name": "Conversational",
                "description": "Natural, everyday speaking style perfect for most content",
                "use_cases": ["Product descriptions", "Tutorials", "General content"]
            },
            {
                "style": VoiceStyle.PROFESSIONAL.value,
                "name": "Professional",
                "description": "Clear, authoritative tone ideal for business content",
                "use_cases": ["Corporate videos", "Training materials", "Presentations"]
            },
            {
                "style": VoiceStyle.ENERGETIC.value,
                "name": "Energetic",
                "description": "Dynamic, enthusiastic delivery for promotional content",
                "use_cases": ["Advertisements", "Product launches", "Sales videos"]
            },
            {
                "style": VoiceStyle.CALM.value,
                "name": "Calm",
                "description": "Soothing, relaxed tone for wellness and lifestyle content",
                "use_cases": ["Meditation", "Wellness products", "Lifestyle content"]
            },
            {
                "style": VoiceStyle.FRIENDLY.value,
                "name": "Friendly",
                "description": "Warm, approachable voice for customer-facing content",
                "use_cases": ["Customer service", "Welcome messages", "Social content"]
            },
            {
                "style": VoiceStyle.AUTHORITATIVE.value,
                "name": "Authoritative",
                "description": "Confident, expert tone for educational and technical content",
                "use_cases": ["Educational content", "Technical explanations", "Expert reviews"]
            }
        ]

        return {
            "styles": styles,
            "total_styles": len(styles)
        }

    except Exception as e:
        logger.exception(f"Failed to list voice styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list voice styles: {str(e)}"
        )


# Media Studio specific endpoints
@router.post("/batches", response_model=BatchGenerationResponse)
async def create_generation_batch(
    request: BatchGenerationRequest,
    background_tasks: BackgroundTasks,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Create a new generation batch for media studio.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        # Create batch using media generation service
        batch = await media_service.create_generation_batch(
            db=db,
            workspace_id=tenant.id,
            request=request
        )

        # Queue background generation tasks
        background_tasks.add_task(
            media_service.process_generation_batch,
            db,
            batch.id
        )

        return BatchGenerationResponse(
            batch_id=batch.id,
            status=batch.status,
            requested_count=batch.requested_count
        )

    except Exception as e:
        logger.error(f"Error creating generation batch: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batches/{batch_id}", response_model=BatchDetailsResponse)
async def get_batch_details(
    batch_id: str,
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get detailed information about a generation batch.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        batch = await media_service.get_batch_with_requests(
            db=db,
            batch_id=batch_id,
            workspace_id=tenant.id
        )

        if not batch:
            raise HTTPException(status_code=404, detail="Batch not found")

        return batch

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting batch details: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/batches", response_model=List[BatchStatusResponse])
async def list_batches(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    List generation batches for the current user.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        batches = await media_service.list_batches(
            db=db,
            workspace_id=tenant.id,
            page=page,
            per_page=per_page
        )

        return batches

    except Exception as e:
        logger.error(f"Error listing batches: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/assets", response_model=AssetsResponse)
async def list_assets(
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100),
    product_id: Optional[str] = Query(None),
    asset_type: Optional[str] = Query(None),
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    List generated assets for the current user.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        assets = await media_service.list_assets(
            db=db,
            workspace_id=tenant.id,
            page=page,
            per_page=per_page,
            product_id=product_id,
            asset_type=asset_type
        )

        return assets

    except Exception as e:
        logger.error(f"Error listing assets: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/usage", response_model=UsageStatsResponse)
async def get_usage_stats(
    db: AsyncSession = Depends(get_db),
    current_user: User = Depends(get_current_user),
):
    """
    Get usage statistics for the current user.
    """
    try:
        # Get user's tenant
        tenant = await db.execute(select(Tenant).filter(Tenant.owner_id == current_user.id))
        tenant = tenant.scalar_one_or_none()
        if not tenant:
            raise HTTPException(
                status_code=404,
                detail="Tenant not found"
            )

        stats = await media_service.get_usage_stats(
            db=db,
            workspace_id=tenant.id
        )

        return stats

    except Exception as e:
        logger.error(f"Error getting usage stats: {e}")
        raise HTTPException(status_code=500, detail=str(e))


@router.get("/voices/script-styles/list")
async def list_tts_script_styles(
    current_user: User = Depends(get_current_user)
):
    """
    List all available script generation styles.
    """
    try:
        script_styles = [
            {
                "style": "promotional",
                "name": "Promotional",
                "description": "Exciting, sales-focused script to drive conversions",
                "tone": "Enthusiastic and persuasive",
                "example": "Introducing the amazing [Product]! Don't miss out - get yours today!"
            },
            {
                "style": "informational",
                "name": "Informational",
                "description": "Educational, fact-based script focusing on features and benefits",
                "tone": "Clear and informative",
                "example": "Let me tell you about [Product]. This product offers [features]."
            },
            {
                "style": "testimonial",
                "name": "Testimonial",
                "description": "Personal, authentic script written from customer perspective",
                "tone": "Personal and authentic",
                "example": "I've been using [Product] and I have to say, it's incredible."
            }
        ]

        return {
            "script_styles": script_styles,
            "total_styles": len(script_styles)
        }

    except Exception as e:
        logger.exception(f"Failed to list script styles: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to list script styles: {str(e)}"
        )


@router.get("/voices/provider/info")
async def get_tts_provider_info(
    current_user: User = Depends(get_current_user)
):
    """
    Get information about the current TTS provider.
    """
    try:
        provider_info = media_service.get_tts_provider_info()

        return {
            "provider_info": provider_info,
            "timestamp": "2025-01-04T14:30:00Z"
        }

    except Exception as e:
        logger.exception(f"Failed to get provider info: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get provider info: {str(e)}"
        )


