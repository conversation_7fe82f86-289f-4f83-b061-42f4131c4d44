"""
Provider Manager for Media Generation Providers.
Handles dynamic loading and management of media providers.
"""

import importlib
import logging
from pathlib import Path
from typing import Dict, List, Optional, Any, Type

from .provider_interface import (
    MediaProviderPlugin,
    ProviderConfig,
    provider_registry
)

logger = logging.getLogger(__name__)


class MediaProviderManager:
    """Manager for loading and managing media providers."""

    def __init__(self, plugins_dir: Optional[str] = None):
        self.plugins_dir = Path(plugins_dir) if plugins_dir else Path(__file__).parent / "providers"
        self.plugins_dir.mkdir(exist_ok=True)
        self._loaded_plugins: Dict[str, Type[MediaProviderPlugin]] = {}

    def discover_providers(self) -> List[str]:
        """Discover available media providers."""
        plugins = []

        # Look for provider modules in the providers directory
        if self.plugins_dir.exists():
            for item in self.plugins_dir.iterdir():
                if item.is_file() and item.suffix == '.py' and not item.name.startswith('_'):
                    plugin_name = item.stem
                    plugins.append(plugin_name)

        logger.info(f"Discovered {len(plugins)} providers: {plugins}")
        return plugins

    async def load_provider(self, provider_name: str) -> Optional[Type[MediaProviderPlugin]]:
        """Load a media provider by name."""
        try:
            # Try to import the plugin module
            module_path = f"modules.media.providers.{plugin_name}"
            module = importlib.import_module(module_path)

            # Look for the provider class
            provider_class = None
            for attr_name in dir(module):
                attr = getattr(module, attr_name)
                if (isinstance(attr, type) and
                    issubclass(attr, MediaProviderPlugin) and
                    attr != MediaProviderPlugin):
                    provider_class = attr
                    break

            if provider_class:
                self._loaded_plugins[plugin_name] = provider_class
                logger.info(f"Loaded provider plugin: {plugin_name}")
                return provider_class
            else:
                logger.error(f"No provider class found in plugin: {plugin_name}")
                return None

        except ImportError as e:
            logger.error(f"Failed to import plugin {plugin_name}: {e}")
            return None
        except Exception as e:
            logger.error(f"Error loading plugin {plugin_name}: {e}")
            return None

    async def load_all_providers(self) -> Dict[str, bool]:
        """Load all discovered providers."""
        providers = self.discover_providers()
        results = {}

        for provider_name in providers:
            provider_class = await self.load_provider(provider_name)
            results[provider_name] = provider_class is not None

        return results

    def get_provider_class(self, provider_name: str) -> Optional[Type[MediaProviderPlugin]]:
        """Get a loaded provider class."""
        return self._loaded_plugins.get(provider_name)

    async def create_provider_instance(
        self,
        provider_name: str,
        config: ProviderConfig
    ) -> Optional[MediaProviderPlugin]:
        """Create an instance of a media provider."""
        provider_class = self.get_provider_class(provider_name)
        if not provider_class:
            logger.error(f"Provider not loaded: {provider_name}")
            return None

        try:
            provider_instance = provider_class()
            success = await provider_instance.initialize(config)

            if success:
                provider_registry.register_provider(provider_instance, config)
                logger.info(f"Registered provider instance: {provider_name}")
                return provider_instance
            else:
                logger.error(f"Failed to initialize provider: {provider_name}")
                return None

        except Exception as e:
            logger.error(f"Error creating provider instance {provider_name}: {e}")
            return None

    def get_loaded_providers(self) -> List[str]:
        """Get list of loaded provider names."""
        return list(self._loaded_plugins.keys())

    async def unload_provider(self, provider_name: str) -> bool:
        """Unload a provider."""
        if provider_name in self._loaded_plugins:
            # Remove from registry if registered
            provider = provider_registry.get_provider(provider_name)
            if provider:
                await provider.cleanup()
                provider_registry.unregister_provider(provider_name)

            del self._loaded_plugins[provider_name]
            logger.info(f"Unloaded provider: {provider_name}")
            return True

        return False

    async def reload_provider(self, provider_name: str) -> bool:
        """Reload a provider."""
        # Unload first
        await self.unload_provider(provider_name)

        # Reload
        provider_class = await self.load_provider(provider_name)
        return provider_class is not None


# Global provider manager instance
provider_manager = MediaProviderManager()


async def initialize_providers(configs: Dict[str, ProviderConfig]) -> Dict[str, bool]:
    """
    Initialize media providers from configuration.

    Args:
        configs: Dictionary mapping provider names to their configurations

    Returns:
        Dictionary with initialization results
    """
    results = {}

    # Load all available providers
    load_results = await provider_manager.load_all_providers()

    # Initialize configured providers
    for provider_name, config in configs.items():
        if provider_name in load_results and load_results[provider_name]:
            provider_instance = await provider_manager.create_provider_instance(provider_name, config)
            results[provider_name] = provider_instance is not None
        else:
            logger.warning(f"Provider {provider_name} not available or failed to load")
            results[provider_name] = False

    return results