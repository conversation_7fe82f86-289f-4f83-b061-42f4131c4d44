"""
Performance Monitoring and Analytics Service for E-commerce Media Generation.
Tracks system performance, quality metrics, and provides optimization insights.
"""

import logging
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum
import asyncio
import json

from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select, func, and_, desc
from sqlalchemy.orm import selectinload

from .models import MediaJob, MediaVariant, GenerationBatch, GeneratedAsset, MediaJobStatus
from .quality_engine import QualityScore

logger = logging.getLogger(__name__)


class MetricType(str, Enum):
    """Types of metrics to track."""
    GENERATION_TIME = "generation_time"
    QUALITY_SCORE = "quality_score"
    SUCCESS_RATE = "success_rate"
    COST_PER_GENERATION = "cost_per_generation"
    USER_SATISFACTION = "user_satisfaction"
    CONVERSION_RATE = "conversion_rate"
    ENGAGEMENT_RATE = "engagement_rate"


class TimeRange(str, Enum):
    """Time range options for analytics."""
    LAST_HOUR = "last_hour"
    LAST_24_HOURS = "last_24_hours"
    LAST_7_DAYS = "last_7_days"
    LAST_30_DAYS = "last_30_days"
    LAST_90_DAYS = "last_90_days"
    CUSTOM = "custom"


@dataclass
class PerformanceMetrics:
    """Performance metrics data structure."""
    # Generation metrics
    total_generations: int
    successful_generations: int
    failed_generations: int
    success_rate: float
    
    # Quality metrics
    average_quality_score: float
    quality_distribution: Dict[str, int]
    
    # Performance metrics
    average_generation_time: float
    median_generation_time: float
    p95_generation_time: float
    
    # Cost metrics
    total_cost: float
    average_cost_per_generation: float
    
    # User engagement
    user_satisfaction_score: float
    regeneration_rate: float
    
    # Provider performance
    provider_performance: Dict[str, Dict[str, float]]
    
    # Trends
    daily_trends: List[Dict[str, Any]]
    hourly_trends: List[Dict[str, Any]]


@dataclass
class QualityInsights:
    """Quality analysis insights."""
    # Quality trends
    quality_trend: str  # "improving", "declining", "stable"
    quality_change_percentage: float
    
    # Common issues
    most_common_issues: List[Tuple[str, int]]
    quality_bottlenecks: List[str]
    
    # Recommendations
    improvement_recommendations: List[str]
    provider_recommendations: Dict[str, str]
    
    # Category performance
    category_quality_scores: Dict[str, float]
    media_type_performance: Dict[str, Dict[str, float]]


@dataclass
class SystemHealth:
    """System health indicators."""
    overall_health_score: float  # 0-100
    
    # Component health
    generation_pipeline_health: float
    quality_engine_health: float
    storage_health: float
    provider_health: Dict[str, float]
    
    # Alerts and warnings
    active_alerts: List[Dict[str, Any]]
    warnings: List[str]
    
    # Resource utilization
    cpu_usage: float
    memory_usage: float
    storage_usage: float
    
    # SLA metrics
    uptime_percentage: float
    average_response_time: float


class MediaAnalyticsService:
    """
    Comprehensive analytics service for media generation system.
    Provides performance monitoring, quality insights, and optimization recommendations.
    """
    
    def __init__(self):
        self.metrics_cache = {}
        self.cache_ttl = 300  # 5 minutes
        self.alert_thresholds = self._load_alert_thresholds()
    
    async def get_performance_metrics(
        self,
        db_session: AsyncSession,
        time_range: TimeRange = TimeRange.LAST_24_HOURS,
        start_date: Optional[datetime] = None,
        end_date: Optional[datetime] = None,
        tenant_id: Optional[str] = None,
        media_type: Optional[str] = None
    ) -> PerformanceMetrics:
        """
        Get comprehensive performance metrics for the specified time range.
        
        Args:
            db_session: Database session
            time_range: Predefined time range or custom
            start_date: Custom start date (for custom time range)
            end_date: Custom end date (for custom time range)
            tenant_id: Filter by specific tenant
            media_type: Filter by media type
            
        Returns:
            Comprehensive performance metrics
        """
        # Calculate date range
        end_dt = end_date or datetime.utcnow()
        start_dt = start_date or self._get_start_date(time_range, end_dt)
        
        # Build base query filters
        filters = [GenerationBatch.created_at.between(start_dt, end_dt)]
        if tenant_id:
            filters.append(GenerationBatch.tenant_id == tenant_id)
        if media_type:
            filters.append(GenerationBatch.media_type == media_type)
        
        # Get generation statistics
        generation_stats = await self._get_generation_statistics(db_session, filters)
        
        # Get quality metrics
        quality_metrics = await self._get_quality_metrics(db_session, filters)
        
        # Get performance metrics
        performance_metrics = await self._get_performance_metrics(db_session, filters)
        
        # Get cost metrics
        cost_metrics = await self._get_cost_metrics(db_session, filters)
        
        # Get user engagement metrics
        engagement_metrics = await self._get_engagement_metrics(db_session, filters)
        
        # Get provider performance
        provider_performance = await self._get_provider_performance(db_session, filters)
        
        # Get trends
        daily_trends = await self._get_daily_trends(db_session, filters, start_dt, end_dt)
        hourly_trends = await self._get_hourly_trends(db_session, filters, start_dt, end_dt)
        
        return PerformanceMetrics(
            total_generations=generation_stats["total"],
            successful_generations=generation_stats["successful"],
            failed_generations=generation_stats["failed"],
            success_rate=generation_stats["success_rate"],
            average_quality_score=quality_metrics["average_score"],
            quality_distribution=quality_metrics["distribution"],
            average_generation_time=performance_metrics["avg_time"],
            median_generation_time=performance_metrics["median_time"],
            p95_generation_time=performance_metrics["p95_time"],
            total_cost=cost_metrics["total_cost"],
            average_cost_per_generation=cost_metrics["avg_cost"],
            user_satisfaction_score=engagement_metrics["satisfaction"],
            regeneration_rate=engagement_metrics["regeneration_rate"],
            provider_performance=provider_performance,
            daily_trends=daily_trends,
            hourly_trends=hourly_trends
        )
    
    async def get_quality_insights(
        self,
        db_session: AsyncSession,
        time_range: TimeRange = TimeRange.LAST_7_DAYS,
        tenant_id: Optional[str] = None
    ) -> QualityInsights:
        """Get quality analysis insights and recommendations."""
        
        # Calculate date ranges for comparison
        end_dt = datetime.utcnow()
        current_start = self._get_start_date(time_range, end_dt)
        previous_start = current_start - (end_dt - current_start)
        
        # Get current period quality metrics
        current_filters = [GenerationBatch.created_at.between(current_start, end_dt)]
        if tenant_id:
            current_filters.append(GenerationBatch.tenant_id == tenant_id)
        
        current_quality = await self._get_quality_metrics(db_session, current_filters)
        
        # Get previous period for comparison
        previous_filters = [GenerationBatch.created_at.between(previous_start, current_start)]
        if tenant_id:
            previous_filters.append(GenerationBatch.tenant_id == tenant_id)
        
        previous_quality = await self._get_quality_metrics(db_session, previous_filters)
        
        # Calculate quality trend
        quality_change = 0.0
        if previous_quality["average_score"] > 0:
            quality_change = ((current_quality["average_score"] - previous_quality["average_score"]) 
                            / previous_quality["average_score"]) * 100
        
        trend = "stable"
        if quality_change > 5:
            trend = "improving"
        elif quality_change < -5:
            trend = "declining"
        
        # Get common issues and bottlenecks
        common_issues = await self._get_common_quality_issues(db_session, current_filters)
        bottlenecks = await self._identify_quality_bottlenecks(db_session, current_filters)
        
        # Generate recommendations
        recommendations = self._generate_quality_recommendations(
            current_quality, common_issues, bottlenecks
        )
        
        # Get category and media type performance
        category_scores = await self._get_category_quality_scores(db_session, current_filters)
        media_type_performance = await self._get_media_type_performance(db_session, current_filters)
        
        # Provider recommendations
        provider_recommendations = await self._get_provider_recommendations(db_session, current_filters)
        
        return QualityInsights(
            quality_trend=trend,
            quality_change_percentage=quality_change,
            most_common_issues=common_issues,
            quality_bottlenecks=bottlenecks,
            improvement_recommendations=recommendations,
            provider_recommendations=provider_recommendations,
            category_quality_scores=category_scores,
            media_type_performance=media_type_performance
        )
    
    async def get_system_health(
        self,
        db_session: AsyncSession
    ) -> SystemHealth:
        """Get current system health status."""
        
        # Calculate component health scores
        generation_health = await self._calculate_generation_pipeline_health(db_session)
        quality_health = await self._calculate_quality_engine_health(db_session)
        storage_health = await self._calculate_storage_health()
        provider_health = await self._calculate_provider_health(db_session)
        
        # Calculate overall health
        overall_health = (
            generation_health * 0.4 +
            quality_health * 0.3 +
            storage_health * 0.2 +
            sum(provider_health.values()) / len(provider_health) * 0.1
        )
        
        # Check for alerts
        alerts = await self._check_system_alerts(db_session)
        warnings = await self._check_system_warnings(db_session)
        
        # Get resource utilization (mock data - would integrate with monitoring system)
        resource_metrics = await self._get_resource_utilization()
        
        # Calculate SLA metrics
        sla_metrics = await self._calculate_sla_metrics(db_session)
        
        return SystemHealth(
            overall_health_score=overall_health,
            generation_pipeline_health=generation_health,
            quality_engine_health=quality_health,
            storage_health=storage_health,
            provider_health=provider_health,
            active_alerts=alerts,
            warnings=warnings,
            cpu_usage=resource_metrics["cpu"],
            memory_usage=resource_metrics["memory"],
            storage_usage=resource_metrics["storage"],
            uptime_percentage=sla_metrics["uptime"],
            average_response_time=sla_metrics["response_time"]
        )
    
    async def track_generation_event(
        self,
        db_session: AsyncSession,
        event_type: str,
        batch_id: str,
        metadata: Dict[str, Any]
    ):
        """Track a generation event for analytics."""
        try:
            # Store event in database or analytics system
            # This would typically go to a time-series database
            logger.info(f"Tracking event: {event_type} for batch {batch_id}")
            
            # Update real-time metrics
            await self._update_realtime_metrics(event_type, metadata)
            
        except Exception as e:
            logger.error(f"Failed to track generation event: {e}")
    
    def _get_start_date(self, time_range: TimeRange, end_date: datetime) -> datetime:
        """Calculate start date based on time range."""
        if time_range == TimeRange.LAST_HOUR:
            return end_date - timedelta(hours=1)
        elif time_range == TimeRange.LAST_24_HOURS:
            return end_date - timedelta(days=1)
        elif time_range == TimeRange.LAST_7_DAYS:
            return end_date - timedelta(days=7)
        elif time_range == TimeRange.LAST_30_DAYS:
            return end_date - timedelta(days=30)
        elif time_range == TimeRange.LAST_90_DAYS:
            return end_date - timedelta(days=90)
        else:
            return end_date - timedelta(days=1)  # Default to 24 hours
    
    async def _get_generation_statistics(
        self,
        db_session: AsyncSession,
        filters: List
    ) -> Dict[str, Any]:
        """Get generation statistics."""
        # Query generation batches
        query = select(
            func.count(GenerationBatch.id).label("total"),
            func.sum(func.case((GenerationBatch.status == "completed", 1), else_=0)).label("successful"),
            func.sum(func.case((GenerationBatch.status == "failed", 1), else_=0)).label("failed")
        ).where(and_(*filters))
        
        result = await db_session.execute(query)
        row = result.first()
        
        total = row.total or 0
        successful = row.successful or 0
        failed = row.failed or 0
        
        return {
            "total": total,
            "successful": successful,
            "failed": failed,
            "success_rate": (successful / total * 100) if total > 0 else 0
        }

    # Mock implementation methods for analytics
    async def _get_quality_metrics(self, db_session: AsyncSession, filters: List) -> Dict[str, Any]:
        """Get quality metrics (mock implementation)."""
        return {
            "average_score": 82.5,
            "distribution": {
                "excellent": 25,
                "good": 45,
                "acceptable": 20,
                "needs_improvement": 10
            }
        }

    async def _get_performance_metrics(self, db_session: AsyncSession, filters: List) -> Dict[str, Any]:
        """Get performance metrics (mock implementation)."""
        return {
            "avg_time": 45.2,
            "median_time": 42.0,
            "p95_time": 78.5
        }

    async def _get_cost_metrics(self, db_session: AsyncSession, filters: List) -> Dict[str, Any]:
        """Get cost metrics (mock implementation)."""
        return {
            "total_cost": 125.50,
            "avg_cost": 0.85
        }

    async def _get_engagement_metrics(self, db_session: AsyncSession, filters: List) -> Dict[str, Any]:
        """Get user engagement metrics (mock implementation)."""
        return {
            "satisfaction": 4.2,
            "regeneration_rate": 12.5
        }

    async def _get_provider_performance(self, db_session: AsyncSession, filters: List) -> Dict[str, Dict[str, float]]:
        """Get provider performance metrics (mock implementation)."""
        return {
            "openai": {"success_rate": 95.2, "avg_quality": 88.5, "avg_time": 35.2},
            "banana": {"success_rate": 92.8, "avg_quality": 85.1, "avg_time": 42.1},
            "mock": {"success_rate": 100.0, "avg_quality": 75.0, "avg_time": 5.0}
        }

    async def _get_daily_trends(self, db_session: AsyncSession, filters: List, start_dt: datetime, end_dt: datetime) -> List[Dict[str, Any]]:
        """Get daily trends (mock implementation)."""
        trends = []
        current_date = start_dt.date()
        while current_date <= end_dt.date():
            trends.append({
                "date": current_date.isoformat(),
                "generations": 45,
                "success_rate": 94.2,
                "avg_quality": 83.1
            })
            current_date += timedelta(days=1)
        return trends

    async def _get_hourly_trends(self, db_session: AsyncSession, filters: List, start_dt: datetime, end_dt: datetime) -> List[Dict[str, Any]]:
        """Get hourly trends (mock implementation)."""
        return [
            {"hour": i, "generations": 15 + (i % 8), "avg_quality": 80 + (i % 10)}
            for i in range(24)
        ]

    async def _get_common_quality_issues(self, db_session: AsyncSession, filters: List) -> List[Tuple[str, int]]:
        """Get common quality issues (mock implementation)."""
        return [
            ("Low lighting quality", 15),
            ("Poor composition", 12),
            ("Color accuracy issues", 8),
            ("Brand compliance", 5)
        ]

    async def _identify_quality_bottlenecks(self, db_session: AsyncSession, filters: List) -> List[str]:
        """Identify quality bottlenecks (mock implementation)."""
        return [
            "Image resolution inconsistency",
            "Prompt engineering for jewelry category",
            "Brand color compliance in lifestyle photos"
        ]

    def _generate_quality_recommendations(self, quality_metrics: Dict, issues: List, bottlenecks: List) -> List[str]:
        """Generate quality improvement recommendations."""
        recommendations = []

        if quality_metrics["average_score"] < 80:
            recommendations.append("Consider upgrading to premium AI providers for better quality")

        if any("lighting" in issue[0].lower() for issue in issues):
            recommendations.append("Improve lighting specifications in prompts")

        if any("composition" in issue[0].lower() for issue in issues):
            recommendations.append("Enhance composition guidelines in prompt engineering")

        return recommendations

    async def _get_category_quality_scores(self, db_session: AsyncSession, filters: List) -> Dict[str, float]:
        """Get quality scores by product category (mock implementation)."""
        return {
            "fashion_apparel": 85.2,
            "footwear": 82.8,
            "accessories": 88.1,
            "jewelry": 79.5,
            "electronics": 83.7
        }

    async def _get_media_type_performance(self, db_session: AsyncSession, filters: List) -> Dict[str, Dict[str, float]]:
        """Get performance by media type (mock implementation)."""
        return {
            "image": {"avg_quality": 84.2, "success_rate": 95.1, "avg_time": 35.5},
            "video": {"avg_quality": 81.8, "success_rate": 92.3, "avg_time": 65.2},
            "copy": {"avg_quality": 87.5, "success_rate": 98.1, "avg_time": 15.8}
        }

    async def _get_provider_recommendations(self, db_session: AsyncSession, filters: List) -> Dict[str, str]:
        """Get provider-specific recommendations (mock implementation)."""
        return {
            "openai": "Excellent performance, consider for premium content",
            "banana": "Good for bulk generation, optimize prompts for better quality",
            "mock": "Use only for testing and development"
        }

    async def _calculate_generation_pipeline_health(self, db_session: AsyncSession) -> float:
        """Calculate generation pipeline health score."""
        # Check recent success rates, error rates, etc.
        return 92.5

    async def _calculate_quality_engine_health(self, db_session: AsyncSession) -> float:
        """Calculate quality engine health score."""
        # Check quality validation performance
        return 88.2

    async def _calculate_storage_health(self) -> float:
        """Calculate storage system health score."""
        # Check storage availability, performance
        return 95.8

    async def _calculate_provider_health(self, db_session: AsyncSession) -> Dict[str, float]:
        """Calculate health scores for each provider."""
        return {
            "openai": 94.2,
            "banana": 89.5,
            "mock": 100.0,
            "elevenlabs": 91.8
        }

    async def _check_system_alerts(self, db_session: AsyncSession) -> List[Dict[str, Any]]:
        """Check for active system alerts."""
        return [
            {
                "id": "alert_001",
                "type": "warning",
                "message": "Provider response time above threshold",
                "severity": "medium",
                "timestamp": datetime.utcnow().isoformat()
            }
        ]

    async def _check_system_warnings(self, db_session: AsyncSession) -> List[str]:
        """Check for system warnings."""
        return [
            "Quality scores trending downward for jewelry category",
            "Storage usage approaching 80% capacity"
        ]

    async def _get_resource_utilization(self) -> Dict[str, float]:
        """Get system resource utilization."""
        return {
            "cpu": 65.2,
            "memory": 72.8,
            "storage": 78.5
        }

    async def _calculate_sla_metrics(self, db_session: AsyncSession) -> Dict[str, float]:
        """Calculate SLA metrics."""
        return {
            "uptime": 99.8,
            "response_time": 2.5
        }

    async def _update_realtime_metrics(self, event_type: str, metadata: Dict[str, Any]):
        """Update real-time metrics."""
        # Update in-memory metrics or send to monitoring system
        logger.info(f"Updated real-time metrics for {event_type}")

    def _load_alert_thresholds(self) -> Dict[str, float]:
        """Load alert threshold configurations."""
        return {
            "success_rate_threshold": 90.0,
            "quality_score_threshold": 75.0,
            "response_time_threshold": 60.0,
            "error_rate_threshold": 5.0
        }


# Create service instance
analytics_service = MediaAnalyticsService()
