"""
Gemini AI service for media generation.
Integrates with Google's Gemini API for image and video generation.
"""

import logging
import asyncio
import httpx
from typing import Dict, List, Optional, Any
from pydantic import BaseModel
from core.config import get_settings

logger = logging.getLogger(__name__)
settings = get_settings()


class GeminiGenerationRequest(BaseModel):
    """Request model for Gemini generation."""
    prompt: str
    mode: str  # 'image' or 'video'
    aspect_ratio: Optional[str] = "1:1"
    quality: Optional[str] = "standard"
    model: str = "gemini-1.5-flash"


class GeminiGenerationResult(BaseModel):
    """Result from Gemini generation."""
    success: bool
    file_url: Optional[str] = None
    preview_url: Optional[str] = None
    error_message: Optional[str] = None
    generation_id: Optional[str] = None


class GeminiService:
    """Service for interacting with Gemini API."""
    
    def __init__(self):
        self.api_key = getattr(settings, 'GEMINI_API_KEY', None)
        self.base_url = "https://generativelanguage.googleapis.com/v1beta"
        self.client = None
    
    async def _get_client(self) -> httpx.AsyncClient:
        """Get or create HTTP client."""
        if self.client is None:
            self.client = httpx.AsyncClient(
                timeout=httpx.Timeout(60.0),
                headers={
                    "Content-Type": "application/json",
                    "Authorization": f"Bearer {self.api_key}" if self.api_key else ""
                }
            )
        return self.client
    
    async def generate_image(self, request: GeminiGenerationRequest) -> GeminiGenerationResult:
        """Generate an image using Gemini."""
        if not self.api_key:
            logger.warning("Gemini API key not configured, using mock response")
            return self._mock_generation_result(request, "image")
        
        try:
            client = await self._get_client()
            
            # Prepare the request payload
            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Generate an image: {request.prompt}"
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            # Make the API call
            response = await client.post(
                f"{self.base_url}/models/{request.model}:generateContent",
                json=payload,
                params={"key": self.api_key}
            )
            
            if response.status_code == 200:
                result = response.json()
                # Extract the generated content
                # Note: This is a simplified implementation
                # In reality, Gemini might return different formats
                content = result.get("candidates", [{}])[0].get("content", {})
                
                # For now, return a mock URL since Gemini text generation
                # doesn't directly generate images
                return GeminiGenerationResult(
                    success=True,
                    file_url=f"https://storage.example.com/generated-image-{hash(request.prompt)}.jpg",
                    preview_url=f"https://storage.example.com/preview-{hash(request.prompt)}.jpg",
                    generation_id=f"gen_{hash(request.prompt)}"
                )
            else:
                logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                return GeminiGenerationResult(
                    success=False,
                    error_message=f"API error: {response.status_code}"
                )
                
        except Exception as e:
            logger.error(f"Error generating image with Gemini: {e}")
            return GeminiGenerationResult(
                success=False,
                error_message=str(e)
            )
    
    async def generate_video(self, request: GeminiGenerationRequest) -> GeminiGenerationResult:
        """Generate a video using Gemini."""
        if not self.api_key:
            logger.warning("Gemini API key not configured, using mock response")
            return self._mock_generation_result(request, "video")
        
        try:
            # Similar to image generation but for video
            # This is a placeholder implementation
            client = await self._get_client()
            
            payload = {
                "contents": [{
                    "parts": [{
                        "text": f"Generate a video concept: {request.prompt}"
                    }]
                }],
                "generationConfig": {
                    "temperature": 0.7,
                    "topK": 40,
                    "topP": 0.95,
                    "maxOutputTokens": 1024,
                }
            }
            
            response = await client.post(
                f"{self.base_url}/models/{request.model}:generateContent",
                json=payload,
                params={"key": self.api_key}
            )
            
            if response.status_code == 200:
                return GeminiGenerationResult(
                    success=True,
                    file_url=f"https://storage.example.com/generated-video-{hash(request.prompt)}.mp4",
                    preview_url=f"https://storage.example.com/preview-{hash(request.prompt)}.jpg",
                    generation_id=f"gen_{hash(request.prompt)}"
                )
            else:
                logger.error(f"Gemini API error: {response.status_code} - {response.text}")
                return GeminiGenerationResult(
                    success=False,
                    error_message=f"API error: {response.status_code}"
                )
                
        except Exception as e:
            logger.error(f"Error generating video with Gemini: {e}")
            return GeminiGenerationResult(
                success=False,
                error_message=str(e)
            )
    
    async def generate_media(self, request: GeminiGenerationRequest) -> GeminiGenerationResult:
        """Generate media based on the mode."""
        if request.mode == "image":
            return await self.generate_image(request)
        elif request.mode == "video":
            return await self.generate_video(request)
        else:
            return GeminiGenerationResult(
                success=False,
                error_message=f"Unsupported mode: {request.mode}"
            )
    
    def _mock_generation_result(self, request: GeminiGenerationRequest, media_type: str) -> GeminiGenerationResult:
        """Create a mock generation result for testing."""
        file_extension = "jpg" if media_type == "image" else "mp4"
        generation_id = f"mock_{hash(request.prompt)}_{media_type}"
        
        return GeminiGenerationResult(
            success=True,
            file_url=f"https://storage.example.com/{generation_id}.{file_extension}",
            preview_url=f"https://storage.example.com/preview_{generation_id}.jpg",
            generation_id=generation_id
        )
    
    async def close(self):
        """Close the HTTP client."""
        if self.client:
            await self.client.aclose()
            self.client = None


# Create singleton instance
gemini_service = GeminiService()
