"""
E-commerce Context Engine for Professional Media Generation.
Analyzes products, brands, and market trends to generate optimal media content.
"""

import logging
import re
from datetime import datetime
from typing import Dict, List, Optional, Any, Tuple
from dataclasses import dataclass
from enum import Enum

from pydantic import BaseModel
import httpx
from sqlalchemy.ext.asyncio import AsyncSession

logger = logging.getLogger(__name__)


class ProductCategory(str, Enum):
    """Product category classifications."""
    FASHION_APPAREL = "fashion_apparel"
    FOOTWEAR = "footwear"
    ACCESSORIES = "accessories"
    BEAUTY_COSMETICS = "beauty_cosmetics"
    HOME_DECOR = "home_decor"
    ELECTRONICS = "electronics"
    JEWELRY = "jewelry"
    SPORTS_FITNESS = "sports_fitness"
    LUXURY_GOODS = "luxury_goods"
    CHILDREN_BABY = "children_baby"


class ContentStyle(str, Enum):
    """Content style preferences."""
    MINIMALIST = "minimalist"
    LUXURY = "luxury"
    LIFESTYLE = "lifestyle"
    EDITORIAL = "editorial"
    SOCIAL_VIRAL = "social_viral"
    PROFESSIONAL = "professional"
    ARTISTIC = "artistic"
    COMMERCIAL = "commercial"


class TargetAudience(str, Enum):
    """Target audience segments."""
    GEN_Z = "gen_z"  # 18-24
    MILLENNIALS = "millennials"  # 25-40
    GEN_X = "gen_x"  # 41-56
    BABY_BOOMERS = "baby_boomers"  # 57+
    LUXURY_BUYERS = "luxury_buyers"
    BUDGET_CONSCIOUS = "budget_conscious"
    EARLY_ADOPTERS = "early_adopters"
    PROFESSIONALS = "professionals"


@dataclass
class ProductContext:
    """Comprehensive product context for media generation."""
    # Basic product info
    title: str
    description: str
    price: Optional[float]
    currency: str = "USD"
    
    # Product attributes
    category: ProductCategory
    subcategory: Optional[str] = None
    brand: Optional[str] = None
    materials: List[str] = None
    colors: List[str] = None
    sizes: List[str] = None
    
    # Visual characteristics
    style_keywords: List[str] = None
    key_features: List[str] = None
    use_cases: List[str] = None
    
    # Market positioning
    price_tier: str = "mid"  # budget, mid, premium, luxury
    target_audience: List[TargetAudience] = None
    seasonal_relevance: List[str] = None
    
    # Existing media
    existing_images: List[str] = None
    existing_videos: List[str] = None
    
    def __post_init__(self):
        if self.materials is None:
            self.materials = []
        if self.colors is None:
            self.colors = []
        if self.sizes is None:
            self.sizes = []
        if self.style_keywords is None:
            self.style_keywords = []
        if self.key_features is None:
            self.key_features = []
        if self.use_cases is None:
            self.use_cases = []
        if self.target_audience is None:
            self.target_audience = []
        if self.seasonal_relevance is None:
            self.seasonal_relevance = []
        if self.existing_images is None:
            self.existing_images = []
        if self.existing_videos is None:
            self.existing_videos = []


@dataclass
class BrandContext:
    """Brand guidelines and style preferences."""
    name: str
    industry: str
    brand_voice: str  # professional, friendly, luxury, edgy, etc.
    color_palette: List[str]
    typography_style: str
    visual_style: ContentStyle
    brand_values: List[str]
    target_demographics: List[TargetAudience]
    content_guidelines: Dict[str, Any]
    competitor_brands: List[str] = None
    
    def __post_init__(self):
        if self.competitor_brands is None:
            self.competitor_brands = []


@dataclass
class MarketContext:
    """Market trends and competitive intelligence."""
    trending_styles: List[str]
    seasonal_trends: List[str]
    competitor_analysis: Dict[str, Any]
    platform_preferences: Dict[str, Any]  # Instagram, TikTok, etc.
    current_campaigns: List[str]
    price_benchmarks: Dict[str, float]


class EcommerceContextEngine:
    """
    Intelligent context engine for e-commerce media generation.
    Analyzes products, brands, and market trends to create optimal content.
    """
    
    def __init__(self):
        self.category_keywords = self._load_category_keywords()
        self.style_patterns = self._load_style_patterns()
        self.trend_data = self._load_trend_data()
    
    async def analyze_product(
        self,
        product_data: Dict[str, Any],
        brand_context: Optional[BrandContext] = None,
        market_context: Optional[MarketContext] = None
    ) -> ProductContext:
        """
        Analyze product data to extract comprehensive context.
        
        Args:
            product_data: Raw product information from e-commerce platform
            brand_context: Brand guidelines and preferences
            market_context: Market trends and competitive data
            
        Returns:
            Comprehensive product context for media generation
        """
        # Extract basic information
        title = product_data.get("title", "")
        description = product_data.get("description", "")
        price = self._extract_price(product_data)
        
        # Classify product category
        category = await self._classify_category(title, description)
        
        # Extract product attributes
        materials = self._extract_materials(title, description)
        colors = self._extract_colors(title, description)
        sizes = self._extract_sizes(product_data)
        
        # Analyze style and features
        style_keywords = self._extract_style_keywords(title, description, category)
        key_features = self._extract_key_features(description, category)
        use_cases = self._generate_use_cases(category, style_keywords)
        
        # Determine market positioning
        price_tier = self._determine_price_tier(price, category)
        target_audience = self._identify_target_audience(
            category, price_tier, style_keywords, brand_context
        )
        
        # Seasonal analysis
        seasonal_relevance = self._analyze_seasonal_relevance(
            category, style_keywords, datetime.now()
        )
        
        # Extract existing media
        existing_images = self._extract_image_urls(product_data)
        existing_videos = self._extract_video_urls(product_data)
        
        return ProductContext(
            title=title,
            description=description,
            price=price,
            category=category,
            brand=brand_context.name if brand_context else None,
            materials=materials,
            colors=colors,
            sizes=sizes,
            style_keywords=style_keywords,
            key_features=key_features,
            use_cases=use_cases,
            price_tier=price_tier,
            target_audience=target_audience,
            seasonal_relevance=seasonal_relevance,
            existing_images=existing_images,
            existing_videos=existing_videos
        )
    
    async def _classify_category(self, title: str, description: str) -> ProductCategory:
        """Classify product into appropriate category."""
        text = f"{title} {description}".lower()
        
        # Category classification rules
        if any(keyword in text for keyword in ["shoe", "boot", "sneaker", "sandal", "heel"]):
            return ProductCategory.FOOTWEAR
        elif any(keyword in text for keyword in ["shirt", "dress", "pants", "jacket", "coat"]):
            return ProductCategory.FASHION_APPAREL
        elif any(keyword in text for keyword in ["bag", "wallet", "belt", "watch", "sunglasses"]):
            return ProductCategory.ACCESSORIES
        elif any(keyword in text for keyword in ["makeup", "skincare", "perfume", "cosmetic"]):
            return ProductCategory.BEAUTY_COSMETICS
        elif any(keyword in text for keyword in ["necklace", "ring", "earring", "bracelet"]):
            return ProductCategory.JEWELRY
        elif any(keyword in text for keyword in ["phone", "laptop", "headphone", "camera"]):
            return ProductCategory.ELECTRONICS
        elif any(keyword in text for keyword in ["home", "decor", "furniture", "lamp"]):
            return ProductCategory.HOME_DECOR
        elif any(keyword in text for keyword in ["fitness", "sport", "gym", "workout"]):
            return ProductCategory.SPORTS_FITNESS
        elif any(keyword in text for keyword in ["baby", "child", "kid", "infant"]):
            return ProductCategory.CHILDREN_BABY
        else:
            return ProductCategory.FASHION_APPAREL  # Default fallback
    
    def _extract_materials(self, title: str, description: str) -> List[str]:
        """Extract material information from product text."""
        text = f"{title} {description}".lower()
        materials = []
        
        material_keywords = [
            "cotton", "silk", "wool", "leather", "denim", "polyester",
            "cashmere", "linen", "velvet", "satin", "chiffon", "lace",
            "metal", "gold", "silver", "platinum", "stainless steel",
            "ceramic", "glass", "wood", "bamboo", "plastic", "rubber"
        ]
        
        for material in material_keywords:
            if material in text:
                materials.append(material)
        
        return materials
    
    def _extract_colors(self, title: str, description: str) -> List[str]:
        """Extract color information from product text."""
        text = f"{title} {description}".lower()
        colors = []
        
        color_keywords = [
            "black", "white", "red", "blue", "green", "yellow", "orange",
            "purple", "pink", "brown", "gray", "grey", "navy", "beige",
            "cream", "gold", "silver", "rose gold", "burgundy", "emerald",
            "sapphire", "coral", "mint", "lavender", "turquoise"
        ]
        
        for color in color_keywords:
            if color in text:
                colors.append(color)
        
        return colors

    def _extract_sizes(self, product_data: Dict[str, Any]) -> List[str]:
        """Extract size information from product data."""
        sizes = []

        # Check variants for size information
        variants = product_data.get("variants", {}).get("edges", [])
        for variant in variants:
            variant_title = variant.get("node", {}).get("title", "").lower()
            # Extract size patterns
            size_patterns = [
                r'\b(xs|s|m|l|xl|xxl|xxxl)\b',
                r'\b(\d+)\b',  # Numeric sizes
                r'\b(small|medium|large|extra large)\b'
            ]

            for pattern in size_patterns:
                matches = re.findall(pattern, variant_title)
                sizes.extend(matches)

        return list(set(sizes))  # Remove duplicates

    def _extract_style_keywords(
        self,
        title: str,
        description: str,
        category: ProductCategory
    ) -> List[str]:
        """Extract style-related keywords."""
        text = f"{title} {description}".lower()
        style_keywords = []

        # General style keywords
        general_styles = [
            "vintage", "modern", "classic", "contemporary", "minimalist",
            "bohemian", "elegant", "casual", "formal", "sporty", "edgy",
            "romantic", "gothic", "preppy", "streetwear", "luxury"
        ]

        # Category-specific keywords
        category_styles = self.style_patterns.get(category, [])

        all_styles = general_styles + category_styles

        for style in all_styles:
            if style in text:
                style_keywords.append(style)

        return style_keywords

    def _extract_key_features(self, description: str, category: ProductCategory) -> List[str]:
        """Extract key product features from description."""
        features = []

        # Common feature patterns
        feature_patterns = {
            ProductCategory.FASHION_APPAREL: [
                "breathable", "stretchy", "wrinkle-free", "machine washable",
                "quick-dry", "moisture-wicking", "UV protection"
            ],
            ProductCategory.FOOTWEAR: [
                "comfortable", "waterproof", "non-slip", "cushioned",
                "arch support", "breathable", "lightweight"
            ],
            ProductCategory.ELECTRONICS: [
                "wireless", "bluetooth", "waterproof", "fast charging",
                "high resolution", "long battery", "portable"
            ]
        }

        category_features = feature_patterns.get(category, [])
        description_lower = description.lower()

        for feature in category_features:
            if feature in description_lower:
                features.append(feature)

        return features

    def _generate_use_cases(
        self,
        category: ProductCategory,
        style_keywords: List[str]
    ) -> List[str]:
        """Generate potential use cases for the product."""
        use_cases = []

        # Category-based use cases
        category_uses = {
            ProductCategory.FASHION_APPAREL: [
                "work", "casual wear", "date night", "travel", "special occasions"
            ],
            ProductCategory.FOOTWEAR: [
                "daily wear", "exercise", "formal events", "outdoor activities"
            ],
            ProductCategory.ACCESSORIES: [
                "everyday use", "travel", "professional settings", "special occasions"
            ]
        }

        base_uses = category_uses.get(category, ["everyday use"])

        # Style-based modifications
        if "formal" in style_keywords:
            use_cases.extend(["business meetings", "formal events", "professional settings"])
        if "casual" in style_keywords:
            use_cases.extend(["weekend wear", "relaxed occasions", "everyday comfort"])
        if "sporty" in style_keywords:
            use_cases.extend(["gym", "outdoor activities", "active lifestyle"])

        return list(set(base_uses + use_cases))

    def _extract_price(self, product_data: Dict[str, Any]) -> Optional[float]:
        """Extract price from product data."""
        # Try different price fields
        price_fields = ["price", "compareAtPrice", "amount"]

        for field in price_fields:
            if field in product_data:
                try:
                    return float(product_data[field])
                except (ValueError, TypeError):
                    continue

        # Try variants
        variants = product_data.get("variants", {}).get("edges", [])
        if variants:
            variant = variants[0].get("node", {})
            price_obj = variant.get("price")
            if price_obj and "amount" in price_obj:
                try:
                    return float(price_obj["amount"])
                except (ValueError, TypeError):
                    pass

        return None

    def _determine_price_tier(
        self,
        price: Optional[float],
        category: ProductCategory
    ) -> str:
        """Determine price tier based on price and category."""
        if price is None:
            return "mid"

        # Category-specific price tiers (in USD)
        tier_thresholds = {
            ProductCategory.FASHION_APPAREL: {"budget": 50, "mid": 150, "premium": 500},
            ProductCategory.FOOTWEAR: {"budget": 80, "mid": 200, "premium": 600},
            ProductCategory.ACCESSORIES: {"budget": 30, "mid": 100, "premium": 300},
            ProductCategory.JEWELRY: {"budget": 100, "mid": 500, "premium": 2000},
            ProductCategory.ELECTRONICS: {"budget": 100, "mid": 500, "premium": 1500}
        }

        thresholds = tier_thresholds.get(category, {"budget": 50, "mid": 150, "premium": 500})

        if price <= thresholds["budget"]:
            return "budget"
        elif price <= thresholds["mid"]:
            return "mid"
        elif price <= thresholds["premium"]:
            return "premium"
        else:
            return "luxury"

    def _identify_target_audience(
        self,
        category: ProductCategory,
        price_tier: str,
        style_keywords: List[str],
        brand_context: Optional[BrandContext]
    ) -> List[TargetAudience]:
        """Identify target audience based on product characteristics."""
        audiences = []

        # Price-based targeting
        if price_tier in ["luxury", "premium"]:
            audiences.append(TargetAudience.LUXURY_BUYERS)
        elif price_tier == "budget":
            audiences.append(TargetAudience.BUDGET_CONSCIOUS)

        # Style-based targeting
        if "trendy" in style_keywords or "streetwear" in style_keywords:
            audiences.append(TargetAudience.GEN_Z)
        if "professional" in style_keywords or "formal" in style_keywords:
            audiences.append(TargetAudience.PROFESSIONALS)
        if "classic" in style_keywords or "elegant" in style_keywords:
            audiences.extend([TargetAudience.MILLENNIALS, TargetAudience.GEN_X])

        # Category-based targeting
        if category == ProductCategory.ELECTRONICS:
            audiences.append(TargetAudience.EARLY_ADOPTERS)

        # Brand context
        if brand_context and brand_context.target_demographics:
            audiences.extend(brand_context.target_demographics)

        return list(set(audiences)) if audiences else [TargetAudience.MILLENNIALS]

    def _analyze_seasonal_relevance(
        self,
        category: ProductCategory,
        style_keywords: List[str],
        current_date: datetime
    ) -> List[str]:
        """Analyze seasonal relevance of the product."""
        seasonal_tags = []
        current_month = current_date.month

        # Seasonal patterns
        if current_month in [12, 1, 2]:  # Winter
            if category == ProductCategory.FASHION_APPAREL:
                if any(keyword in style_keywords for keyword in ["warm", "cozy", "winter"]):
                    seasonal_tags.append("winter_essential")
            seasonal_tags.append("holiday_gift")

        elif current_month in [3, 4, 5]:  # Spring
            seasonal_tags.append("spring_refresh")
            if "floral" in style_keywords:
                seasonal_tags.append("spring_trend")

        elif current_month in [6, 7, 8]:  # Summer
            if category == ProductCategory.FOOTWEAR and "sandal" in style_keywords:
                seasonal_tags.append("summer_essential")
            seasonal_tags.append("vacation_ready")

        elif current_month in [9, 10, 11]:  # Fall
            seasonal_tags.append("back_to_school")
            if "warm" in style_keywords:
                seasonal_tags.append("fall_layering")

        return seasonal_tags

    def _extract_image_urls(self, product_data: Dict[str, Any]) -> List[str]:
        """Extract existing image URLs from product data."""
        images = []

        # Check images field
        if "images" in product_data:
            image_edges = product_data["images"].get("edges", [])
            for edge in image_edges:
                url = edge.get("node", {}).get("url")
                if url:
                    images.append(url)

        return images

    def _extract_video_urls(self, product_data: Dict[str, Any]) -> List[str]:
        """Extract existing video URLs from product data."""
        videos = []

        # Check media field for videos
        if "media" in product_data:
            media_edges = product_data["media"].get("edges", [])
            for edge in media_edges:
                node = edge.get("node", {})
                if node.get("mediaContentType") == "VIDEO":
                    sources = node.get("sources", [])
                    if sources:
                        videos.append(sources[0].get("url"))

        return videos

    def _load_category_keywords(self) -> Dict[ProductCategory, List[str]]:
        """Load category-specific keywords."""
        return {
            ProductCategory.FASHION_APPAREL: [
                "trendy", "stylish", "comfortable", "versatile", "chic"
            ],
            ProductCategory.FOOTWEAR: [
                "comfortable", "durable", "stylish", "supportive", "lightweight"
            ],
            ProductCategory.ACCESSORIES: [
                "elegant", "practical", "stylish", "versatile", "premium"
            ]
        }

    def _load_style_patterns(self) -> Dict[ProductCategory, List[str]]:
        """Load style patterns for each category."""
        return {
            ProductCategory.FASHION_APPAREL: [
                "boho", "preppy", "grunge", "minimalist", "maximalist"
            ],
            ProductCategory.FOOTWEAR: [
                "athletic", "dress", "casual", "outdoor", "fashion"
            ]
        }

    def _load_trend_data(self) -> Dict[str, Any]:
        """Load current trend data."""
        return {
            "colors": ["sage green", "digital lime", "classic blue"],
            "styles": ["oversized", "cropped", "vintage-inspired"],
            "materials": ["sustainable", "recycled", "organic"]
        }


# Create service instance
context_engine = EcommerceContextEngine()
