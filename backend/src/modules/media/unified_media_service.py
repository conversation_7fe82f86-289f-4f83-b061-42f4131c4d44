"""
Unified Professional Media Service for E-commerce.
Consolidates all media generation capabilities with professional-grade quality assurance.
"""

import logging
from typing import Dict, List, Optional, Any, Union
from dataclasses import dataclass
from enum import Enum
import asyncio

from sqlalchemy.ext.asyncio import AsyncSession

from .context_engine import EcommerceContextEngine, ProductContext, BrandContext, MarketContext
from .prompt_engine import ProfessionalPromptEngine, MediaType, Platform, PromptContext, GeneratedPrompt
from .quality_engine import ProfessionalQualityEngine, ValidationResult, QualityScore
from .service import media_service  # Original service for provider management
from .schemas import MediaGenerationRequest, MediaGenerationResult
from .models import MediaJob, MediaVariant, GenerationBatch

logger = logging.getLogger(__name__)


class GenerationMode(str, Enum):
    """Media generation modes."""
    STANDARD = "standard"  # Basic generation
    PROFESSIONAL = "professional"  # With context analysis and quality assurance
    ENTERPRISE = "enterprise"  # Full pipeline with validation and optimization


@dataclass
class UnifiedMediaRequest:
    """Unified request for professional media generation."""
    # Basic requirements
    media_type: MediaType
    product_data: Dict[str, Any]
    
    # Generation settings
    generation_mode: GenerationMode = GenerationMode.PROFESSIONAL
    platform: Optional[Platform] = None
    aspect_ratio: str = "1:1"
    variants_count: int = 4
    
    # Context and customization
    brand_context: Optional[BrandContext] = None
    market_context: Optional[MarketContext] = None
    custom_prompt: Optional[str] = None
    style_preferences: Optional[Dict[str, Any]] = None
    
    # Quality requirements
    minimum_quality_score: float = 75.0
    require_brand_compliance: bool = True
    auto_approve_threshold: float = 85.0
    
    # Campaign and tracking
    campaign_id: Optional[str] = None
    batch_id: Optional[str] = None
    tenant_id: Optional[str] = None


@dataclass
class UnifiedMediaResult:
    """Unified result for professional media generation."""
    success: bool
    media_variants: List[Dict[str, Any]]
    
    # Quality and validation
    quality_scores: List[float]
    validation_results: List[ValidationResult]
    approved_variants: List[Dict[str, Any]]
    
    # Context and insights
    product_context: Optional[ProductContext] = None
    generated_prompts: List[GeneratedPrompt] = None
    performance_predictions: List[Dict[str, Any]] = None
    
    # Tracking and metadata
    generation_batch_id: Optional[str] = None
    processing_time_seconds: float = 0.0
    total_cost: float = 0.0
    
    # Issues and recommendations
    issues_found: List[str] = None
    recommendations: List[str] = None
    error_message: Optional[str] = None
    
    def __post_init__(self):
        if self.issues_found is None:
            self.issues_found = []
        if self.recommendations is None:
            self.recommendations = []


class UnifiedProfessionalMediaService:
    """
    Unified professional media service for e-commerce.
    Provides enterprise-grade media generation with context analysis and quality assurance.
    """
    
    def __init__(self):
        self.context_engine = EcommerceContextEngine()
        self.prompt_engine = ProfessionalPromptEngine()
        self.quality_engine = ProfessionalQualityEngine()
        
        # Performance tracking
        self.generation_stats = {
            "total_requests": 0,
            "successful_generations": 0,
            "quality_scores": [],
            "processing_times": []
        }
    
    async def generate_media(
        self,
        request: UnifiedMediaRequest,
        db_session: Optional[AsyncSession] = None
    ) -> UnifiedMediaResult:
        """
        Generate professional media with full context analysis and quality assurance.
        
        Args:
            request: Unified media generation request
            db_session: Database session for tracking
            
        Returns:
            Comprehensive media generation result with quality metrics
        """
        start_time = asyncio.get_event_loop().time()
        
        try:
            # Track request
            self.generation_stats["total_requests"] += 1
            
            # Step 1: Analyze product context
            logger.info(f"Analyzing product context for {request.media_type}")
            product_context = await self.context_engine.analyze_product(
                request.product_data,
                request.brand_context,
                request.market_context
            )
            
            # Step 2: Generate professional prompts
            logger.info("Generating professional prompts")
            prompt_context = PromptContext(
                media_type=request.media_type,
                platform=request.platform,
                aspect_ratio=request.aspect_ratio,
                style_preference=request.style_preferences.get("content_style") if request.style_preferences else None
            )
            
            generated_prompt = await self.prompt_engine.generate_prompt(
                product_context,
                prompt_context,
                request.brand_context,
                request.market_context
            )
            
            # Step 3: Generate media variants
            logger.info(f"Generating {request.variants_count} media variants")
            media_variants = []
            generation_results = []
            
            for i in range(request.variants_count):
                # Create generation request for original service
                media_request = MediaGenerationRequest(
                    media_type=request.media_type.value,
                    product_id=request.product_data.get("id", "unknown"),
                    custom_config={
                        "prompt": request.custom_prompt or generated_prompt.main_prompt,
                        "negative_prompt": generated_prompt.negative_prompt,
                        "aspect_ratio": request.aspect_ratio,
                        "style_modifiers": generated_prompt.style_modifiers,
                        **generated_prompt.technical_specs,
                        **(request.style_preferences or {})
                    }
                )
                
                # Generate using appropriate provider
                provider_name = media_service._get_provider_for_media_type(request.media_type.value)
                result = await media_service.generate_media_with_provider(
                    provider_name, media_request
                )
                
                if result.success:
                    generation_results.append(result)
                    if hasattr(result, 'variants') and result.variants:
                        media_variants.extend(result.variants)
                    elif hasattr(result, 'images') and result.images:
                        media_variants.extend(result.images)
                else:
                    logger.warning(f"Variant {i+1} generation failed: {result.error_message}")
            
            if not media_variants:
                return UnifiedMediaResult(
                    success=False,
                    media_variants=[],
                    quality_scores=[],
                    validation_results=[],
                    approved_variants=[],
                    error_message="No media variants were successfully generated"
                )
            
            # Step 4: Quality assessment and validation (if not standard mode)
            validation_results = []
            quality_scores = []
            approved_variants = []
            
            if request.generation_mode != GenerationMode.STANDARD:
                logger.info("Performing quality assessment and validation")
                
                for variant in media_variants:
                    content_url = variant.get("url") or variant.get("image_url") or variant.get("video_url")
                    
                    if content_url:
                        validation_result = await self.quality_engine.validate_content(
                            content_url,
                            request.media_type,
                            product_context,
                            request.brand_context,
                            generated_prompt
                        )
                        
                        validation_results.append(validation_result)
                        quality_scores.append(validation_result.quality_metrics.overall_score)
                        
                        # Auto-approve high-quality content
                        if (validation_result.quality_metrics.overall_score >= request.auto_approve_threshold and
                            validation_result.approval_confidence >= 80 and
                            not validation_result.content_flags):
                            approved_variants.append({
                                **variant,
                                "quality_score": validation_result.quality_metrics.overall_score,
                                "validation_status": "approved",
                                "auto_approved": True
                            })
                        elif validation_result.quality_metrics.overall_score >= request.minimum_quality_score:
                            approved_variants.append({
                                **variant,
                                "quality_score": validation_result.quality_metrics.overall_score,
                                "validation_status": "needs_review",
                                "auto_approved": False
                            })
            else:
                # Standard mode - approve all variants
                approved_variants = media_variants
                quality_scores = [75.0] * len(media_variants)  # Default score
            
            # Step 5: Performance predictions
            performance_predictions = []
            if validation_results:
                performance_predictions = [vr.estimated_performance for vr in validation_results]
            
            # Step 6: Generate insights and recommendations
            issues_found = []
            recommendations = []
            
            if validation_results:
                for vr in validation_results:
                    issues_found.extend(vr.quality_metrics.issues_found)
                    recommendations.extend(vr.quality_metrics.recommendations)
            
            # Remove duplicates
            issues_found = list(set(issues_found))
            recommendations = list(set(recommendations))
            
            # Step 7: Track in database (if session provided)
            generation_batch_id = None
            if db_session:
                generation_batch_id = await self._track_generation_batch(
                    db_session, request, product_context, media_variants, validation_results
                )
            
            # Calculate processing time and update stats
            processing_time = asyncio.get_event_loop().time() - start_time
            self.generation_stats["processing_times"].append(processing_time)
            self.generation_stats["successful_generations"] += 1
            self.generation_stats["quality_scores"].extend(quality_scores)
            
            logger.info(f"Media generation completed in {processing_time:.2f}s with {len(approved_variants)} approved variants")
            
            return UnifiedMediaResult(
                success=True,
                media_variants=media_variants,
                quality_scores=quality_scores,
                validation_results=validation_results,
                approved_variants=approved_variants,
                product_context=product_context,
                generated_prompts=[generated_prompt],
                performance_predictions=performance_predictions,
                generation_batch_id=generation_batch_id,
                processing_time_seconds=processing_time,
                issues_found=issues_found,
                recommendations=recommendations
            )
            
        except Exception as e:
            logger.error(f"Media generation failed: {e}")
            processing_time = asyncio.get_event_loop().time() - start_time
            
            return UnifiedMediaResult(
                success=False,
                media_variants=[],
                quality_scores=[],
                validation_results=[],
                approved_variants=[],
                processing_time_seconds=processing_time,
                error_message=str(e)
            )
    
    async def _track_generation_batch(
        self,
        db_session: AsyncSession,
        request: UnifiedMediaRequest,
        product_context: ProductContext,
        media_variants: List[Dict[str, Any]],
        validation_results: List[ValidationResult]
    ) -> str:
        """Track generation batch in database."""
        try:
            # Create generation batch record
            batch = GenerationBatch(
                tenant_id=request.tenant_id,
                campaign_id=request.campaign_id,
                media_type=request.media_type.value,
                product_id=request.product_data.get("id"),
                variants_count=len(media_variants),
                generation_mode=request.generation_mode.value,
                status="completed"
            )
            
            db_session.add(batch)
            await db_session.flush()  # Get the ID
            
            # Track individual variants
            for i, variant in enumerate(media_variants):
                validation_result = validation_results[i] if i < len(validation_results) else None
                
                variant_record = MediaVariant(
                    batch_id=batch.id,
                    variant_index=i,
                    media_url=variant.get("url") or variant.get("image_url") or variant.get("video_url"),
                    quality_score=validation_result.quality_metrics.overall_score if validation_result else 75.0,
                    validation_status=validation_result.status.value if validation_result else "approved",
                    metadata=variant
                )
                
                db_session.add(variant_record)
            
            await db_session.commit()
            return str(batch.id)
            
        except Exception as e:
            logger.error(f"Failed to track generation batch: {e}")
            await db_session.rollback()
            return None
    
    def get_performance_stats(self) -> Dict[str, Any]:
        """Get service performance statistics."""
        avg_quality = sum(self.generation_stats["quality_scores"]) / len(self.generation_stats["quality_scores"]) if self.generation_stats["quality_scores"] else 0
        avg_processing_time = sum(self.generation_stats["processing_times"]) / len(self.generation_stats["processing_times"]) if self.generation_stats["processing_times"] else 0
        
        return {
            "total_requests": self.generation_stats["total_requests"],
            "successful_generations": self.generation_stats["successful_generations"],
            "success_rate": self.generation_stats["successful_generations"] / max(1, self.generation_stats["total_requests"]),
            "average_quality_score": avg_quality,
            "average_processing_time": avg_processing_time,
            "quality_distribution": {
                "excellent": len([s for s in self.generation_stats["quality_scores"] if s >= 90]),
                "good": len([s for s in self.generation_stats["quality_scores"] if 75 <= s < 90]),
                "acceptable": len([s for s in self.generation_stats["quality_scores"] if 60 <= s < 75]),
                "needs_improvement": len([s for s in self.generation_stats["quality_scores"] if s < 60])
            }
        }


# Create service instance
unified_media_service = UnifiedProfessionalMediaService()
