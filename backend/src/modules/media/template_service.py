"""
Template Service for ProductVideo platform.
Handles template discovery, filtering, and customization options.
"""

import logging
from typing import Dict, List, Optional, Any

from sqlalchemy import select
from sqlalchemy.ext.asyncio import AsyncSession

from .models import Template

logger = logging.getLogger(__name__)


class TemplateService:
    """Service for managing video templates."""
    
    # Mock template data - in production this would come from database or external API
    MOCK_TEMPLATES = [
        {
            "id": "modern_product_showcase",
            "name": "Modern Product Showcase",
            "description": "Clean, modern template perfect for showcasing products with smooth transitions",
            "category": "product",
            "aspect_ratios": ["16:9", "9:16", "1:1", "4:5"],
            "duration_range": [15, 30],
            "is_premium": False,
            "preview_url": "https://templates.productvideo.ai/previews/modern_product_showcase.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/modern_product_showcase.jpg",
            "features": ["Product zoom", "Text overlays", "Background music", "Call-to-action"],
            "customizable_elements": ["background_color", "text_style", "music_track", "transition_speed"]
        },
        {
            "id": "dynamic_lifestyle",
            "name": "Dynamic Lifestyle",
            "description": "High-energy template with dynamic transitions and lifestyle imagery",
            "category": "lifestyle",
            "aspect_ratios": ["16:9", "9:16", "1:1"],
            "duration_range": [20, 45],
            "is_premium": True,
            "preview_url": "https://templates.productvideo.ai/previews/dynamic_lifestyle.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/dynamic_lifestyle.jpg",
            "features": ["Lifestyle scenes", "Product integration", "Dynamic text", "Premium music"],
            "customizable_elements": ["lifestyle_theme", "color_palette", "text_animation", "music_genre"]
        },
        {
            "id": "minimalist_clean",
            "name": "Minimalist Clean",
            "description": "Simple, elegant template focusing on product details with minimal distractions",
            "category": "minimalist",
            "aspect_ratios": ["16:9", "9:16", "1:1", "4:5"],
            "duration_range": [10, 25],
            "is_premium": False,
            "preview_url": "https://templates.productvideo.ai/previews/minimalist_clean.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/minimalist_clean.jpg",
            "features": ["Clean layout", "Product focus", "Subtle animations", "Professional look"],
            "customizable_elements": ["background_style", "text_placement", "animation_speed"]
        },
        {
            "id": "social_media_viral",
            "name": "Social Media Viral",
            "description": "Trendy template optimized for social media engagement and viral potential",
            "category": "social",
            "aspect_ratios": ["9:16", "1:1", "4:5"],
            "duration_range": [15, 30],
            "is_premium": True,
            "preview_url": "https://templates.productvideo.ai/previews/social_media_viral.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/social_media_viral.jpg",
            "features": ["Trending effects", "Social hooks", "Engagement elements", "Mobile-first"],
            "customizable_elements": ["trend_style", "hook_text", "effect_intensity", "social_platform"]
        },
        {
            "id": "luxury_premium",
            "name": "Luxury Premium",
            "description": "Sophisticated template for high-end products with premium aesthetics",
            "category": "luxury",
            "aspect_ratios": ["16:9", "9:16", "1:1"],
            "duration_range": [25, 60],
            "is_premium": True,
            "preview_url": "https://templates.productvideo.ai/previews/luxury_premium.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/luxury_premium.jpg",
            "features": ["Luxury aesthetics", "Premium transitions", "Elegant typography", "High-end feel"],
            "customizable_elements": ["luxury_theme", "typography_style", "transition_elegance", "color_scheme"]
        },
        {
            "id": "ecommerce_conversion",
            "name": "E-commerce Conversion",
            "description": "Conversion-optimized template with clear CTAs and product benefits",
            "category": "ecommerce",
            "aspect_ratios": ["16:9", "9:16", "1:1", "4:5"],
            "duration_range": [20, 40],
            "is_premium": False,
            "preview_url": "https://templates.productvideo.ai/previews/ecommerce_conversion.mp4",
            "thumbnail_url": "https://templates.productvideo.ai/thumbnails/ecommerce_conversion.jpg",
            "features": ["Clear CTAs", "Benefit highlights", "Price display", "Urgency elements"],
            "customizable_elements": ["cta_style", "benefit_layout", "price_display", "urgency_type"]
        }
    ]
    
    async def get_templates(
        self,
        db: AsyncSession,
        category: Optional[str] = None,
        aspect_ratio: Optional[str] = None,
        is_premium: Optional[bool] = None,
        user_plan: str = "free",
        page: int = 1,
        per_page: int = 20
    ) -> Dict[str, Any]:
        """
        Get filtered list of templates.
        
        Args:
            db: Database session
            category: Filter by category
            aspect_ratio: Filter by aspect ratio
            is_premium: Filter by premium status
            user_plan: User's plan tier
            page: Page number
            per_page: Items per page
            
        Returns:
            Filtered templates with pagination
        """
        # Filter templates
        filtered_templates = self.MOCK_TEMPLATES.copy()
        
        # Filter by category
        if category:
            filtered_templates = [t for t in filtered_templates if t["category"] == category]
        
        # Filter by aspect ratio
        if aspect_ratio:
            filtered_templates = [t for t in filtered_templates if aspect_ratio in t["aspect_ratios"]]
        
        # Filter by premium status
        if is_premium is not None:
            filtered_templates = [t for t in filtered_templates if t["is_premium"] == is_premium]
        
        # Filter by user plan (free users can't access premium templates)
        if user_plan == "free":
            filtered_templates = [t for t in filtered_templates if not t["is_premium"]]
        
        # Pagination
        total = len(filtered_templates)
        start_idx = (page - 1) * per_page
        end_idx = start_idx + per_page
        paginated_templates = filtered_templates[start_idx:end_idx]
        
        return {
            "templates": paginated_templates,
            "total": total,
            "page": page,
            "per_page": per_page,
            "total_pages": (total + per_page - 1) // per_page
        }
    
    async def get_template_by_id(self, db: AsyncSession, template_id: str) -> Optional[Dict[str, Any]]:
        """Get template by ID."""
        for template in self.MOCK_TEMPLATES:
            if template["id"] == template_id:
                return template
        return None
    
    async def get_template_categories(self, db: AsyncSession) -> List[Dict[str, Any]]:
        """Get available template categories."""
        categories = {}
        for template in self.MOCK_TEMPLATES:
            category = template["category"]
            if category not in categories:
                categories[category] = {
                    "name": category.title(),
                    "count": 0,
                    "description": self._get_category_description(category)
                }
            categories[category]["count"] += 1
        
        return list(categories.values())
    
    def _get_category_description(self, category: str) -> str:
        """Get description for a category."""
        descriptions = {
            "product": "Templates focused on showcasing individual products",
            "lifestyle": "Templates that integrate products into lifestyle scenarios",
            "minimalist": "Clean, simple templates with minimal design elements",
            "social": "Templates optimized for social media platforms",
            "luxury": "Premium templates for high-end products and brands",
            "ecommerce": "Conversion-focused templates for online stores"
        }
        return descriptions.get(category, f"Templates in the {category} category")
    
    async def get_customization_options(
        self, 
        db: AsyncSession, 
        template_id: str
    ) -> Optional[Dict[str, Any]]:
        """Get customization options for a template."""
        template = await self.get_template_by_id(db, template_id)
        if not template:
            return None
        
        # Mock customization options
        customization_options = {
            "background_color": {
                "type": "color",
                "label": "Background Color",
                "options": ["#ffffff", "#000000", "#f5f5f5", "#e8e8e8"],
                "default": "#ffffff"
            },
            "text_style": {
                "type": "select",
                "label": "Text Style",
                "options": ["modern", "classic", "bold", "elegant"],
                "default": "modern"
            },
            "music_track": {
                "type": "select",
                "label": "Background Music",
                "options": ["upbeat", "calm", "energetic", "none"],
                "default": "upbeat"
            },
            "transition_speed": {
                "type": "range",
                "label": "Transition Speed",
                "min": 0.5,
                "max": 2.0,
                "step": 0.1,
                "default": 1.0
            }
        }
        
        # Filter options based on template's customizable elements
        available_options = {}
        for element in template.get("customizable_elements", []):
            if element in customization_options:
                available_options[element] = customization_options[element]
        
        return {
            "template_id": template_id,
            "options": available_options
        }


# Create service instance
template_service = TemplateService()
