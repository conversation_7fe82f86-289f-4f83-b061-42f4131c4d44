"""
ProductVideo platform data models.
Multi-tenant models for video generation, analytics, and billing.
"""

from sqlalchemy import <PERSON>umn, Integer, String, DateTime, Float, Boolean, Text, ForeignKey, JSON, Enum
from sqlalchemy.sql import func
from sqlalchemy.orm import relationship
from enum import Enum as PyEnum

from core.db.database import Base


class MediaJobStatus(PyEnum):
    """Media job status enumeration."""
    PENDING = "pending"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class MediaVariantStatus(PyEnum):
    """Media variant status enumeration."""
    GENERATING = "generating"
    READY = "ready"
    FAILED = "failed"
    PROCESSING = "processing"


class PushStatus(PyEnum):
    """Push status enumeration."""
    PENDING = "pending"
    PUSHING = "pushing"
    COMPLETED = "completed"
    FAILED = "failed"


class PlanTier(PyEnum):
    """Billing plan tiers."""
    FREE = "free"
    STARTER = "starter"
    GROWTH = "growth"
    PRO = "pro"
    ENTERPRISE = "enterprise"


class ImageProvider(PyEnum):
    """Image generation providers."""
    MOCK = "mock"
    BANANA = "banana"
    DALL_E = "dall_e"
    MIDJOURNEY = "midjourney"


class VideoProvider(PyEnum):
    """Video generation providers."""
    MOCK = "mock"
    VEO3 = "veo3"
    REVID_AI = "revid_ai"
    VIDIFY = "vidify"
    PRODUCT_STUDIO = "product_studio"


class VoiceProvider(PyEnum):
    """Voice generation providers."""
    MOCK = "mock"
    GOOGLE_WAVENET = "google_wavenet"
    ELEVEN_LABS = "eleven_labs"


class BaseMediaProvider:
    """Base class for media generation providers."""

    def __init__(self, api_key: str):
        self.api_key = api_key
        self.client = None  # httpx.AsyncClient or similar

    async def generate(self, request):
        """Generate media. To be implemented by subclasses."""
        raise NotImplementedError

    async def get_job_status(self, job_id: str):
        """Get job status. To be implemented by subclasses."""
        raise NotImplementedError

    async def download_media(self, media_url: str):
        """Download media. To be implemented by subclasses."""
        raise NotImplementedError


class MediaJob(Base):
    """
    Media generation job tracking.
    Each job represents a request to generate media for a product.
    """
    __tablename__ = 'media_jobs'

    id = Column(Integer, primary_key=True, index=True)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    product_id = Column(String, nullable=False)  # External product ID from e-commerce platform (e.g., Shopify product ID)
    status = Column(Enum(MediaJobStatus), default=MediaJobStatus.PENDING, nullable=False)
    media_type = Column(String, nullable=False) # 'video', 'image', 'voice'
    provider = Column(String, nullable=False)  # revid_ai, vidify, product_studio

    # Job configuration
    template_id = Column(String, nullable=True)
    voice_id = Column(String, nullable=True)
    script = Column(Text, nullable=True)
    custom_config = Column(JSON, nullable=True)

    # Progress tracking
    progress_percentage = Column(Float, default=0.0)
    error_message = Column(Text, nullable=True)

    # Timestamps
    started_at = Column(DateTime(timezone=True), nullable=True)
    completed_at = Column(DateTime(timezone=True), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    tenant = relationship("Tenant", back_populates="media_jobs")

    def __repr__(self):
        return f"<MediaJob(id={self.id}, product_id='{self.product_id}', status='{self.status.value}')>"


class MediaVariant(Base):
    """
    Individual media variants generated from a job.
    A single job can produce multiple media variants.
    """
    __tablename__ = 'media_variants'

    id = Column(Integer, primary_key=True, index=True)
    job_id = Column(Integer, ForeignKey('media_jobs.id'), nullable=False)
    tenant_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)  # Added for filtering
    external_variant_id = Column(String, nullable=True)  # External ID for API
    variant_name = Column(String, nullable=False)  # e.g., "square", "vertical", "horizontal"
    status = Column(Enum(MediaVariantStatus), default=MediaVariantStatus.GENERATING, nullable=False)

    # Media details
    duration_seconds = Column(Float, nullable=True)
    resolution = Column(String, nullable=True)  # e.g., "1080x1920"
    file_size_bytes = Column(Integer, nullable=True)

    # Storage URLs
    video_url = Column(String, nullable=True)
    image_url = Column(String, nullable=True)
    voice_url = Column(String, nullable=True)
    thumbnail_url = Column(String, nullable=True)

    # Provider-specific data
    provider_media_id = Column(String, nullable=True)
    provider_metadata = Column(JSON, nullable=True)

    # User interaction
    is_favorite = Column(Boolean, default=False)
    user_rating = Column(Integer, nullable=True)  # 1-5 stars

    # Platform integration
    push_status = Column(Enum(PushStatus), default=PushStatus.PENDING, nullable=False)
    media_id = Column(String, nullable=True)
    pushed_at = Column(DateTime(timezone=True), nullable=True)
    push_error_message = Column(Text, nullable=True)
    alt_text = Column(String, nullable=True)

    # Error handling
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<MediaVariant(id={self.id}, variant_name='{self.variant_name}', status='{self.status.value}')>"


class Template(Base):
    """
    Video templates for different use cases.
    Templates define the structure and style of generated videos.
    """
    __tablename__ = 'templates'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    description = Column(Text, nullable=True)
    provider = Column(String, nullable=False)  # Which provider this template belongs to
    
    # Template configuration
    template_config = Column(JSON, nullable=False)  # Provider-specific template data
    preview_url = Column(String, nullable=True)
    
    # Categorization
    category = Column(String, nullable=True)  # e.g., "product_showcase", "testimonial"
    tags = Column(JSON, nullable=True)  # Array of tags for filtering
    
    # Availability
    is_active = Column(Boolean, default=True)
    plan_tier_required = Column(Enum(PlanTier), default=PlanTier.FREE, nullable=False)
    
    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Template(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class Voice(Base):
    """
    Available voices for video narration.
    """
    __tablename__ = 'voices'

    id = Column(Integer, primary_key=True, index=True)
    name = Column(String, nullable=False)
    provider = Column(String, nullable=False)  # Which provider this voice belongs to
    provider_voice_id = Column(String, nullable=False)  # Provider's internal voice ID

    # Voice characteristics
    gender = Column(String, nullable=True)  # male, female, neutral
    accent = Column(String, nullable=True)  # american, british, australian, etc.
    language = Column(String, nullable=False, default="en")

    # Sample and preview
    sample_url = Column(String, nullable=True)
    description = Column(Text, nullable=True)

    # Availability
    is_active = Column(Boolean, default=True)
    plan_tier_required = Column(Enum(PlanTier), default=PlanTier.FREE, nullable=False)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    def __repr__(self):
        return f"<Voice(id={self.id}, name='{self.name}', provider='{self.provider}')>"


class GenerationBatch(Base):
    """
    Media generation batch tracking for media studio.
    Each batch represents a group of generation requests.
    """
    __tablename__ = 'generation_batches'

    id = Column(String, primary_key=True, index=True)  # UUID
    workspace_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    mode = Column(String, nullable=False)  # 'image' or 'video'
    aspect_ratio = Column(String, nullable=True)
    quality = Column(String, nullable=True)
    model = Column(String, nullable=False)
    requested_count = Column(Integer, default=0)
    completed_count = Column(Integer, default=0)
    failed_count = Column(Integer, default=0)
    status = Column(String, default="pending")  # pending, processing, completed, failed

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    workspace = relationship("Tenant", back_populates="generation_batches")
    requests = relationship("GenerationRequest", back_populates="batch")

    def __repr__(self):
        return f"<GenerationBatch(id='{self.id}', mode='{self.mode}', status='{self.status}')>"


class GenerationRequest(Base):
    """
    Individual generation requests within a batch.
    """
    __tablename__ = 'generation_requests'

    id = Column(String, primary_key=True, index=True)  # UUID
    batch_id = Column(String, ForeignKey('generation_batches.id'), nullable=False)
    product_id = Column(String, nullable=False)
    variant_id = Column(String, nullable=True)
    prompt = Column(Text, nullable=False)
    params_json = Column(JSON, nullable=True)
    status = Column(String, default="pending")  # pending, processing, completed, failed
    result_url = Column(String, nullable=True)
    error_message = Column(Text, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    batch = relationship("GenerationBatch", back_populates="requests")

    def __repr__(self):
        return f"<GenerationRequest(id='{self.id}', product_id='{self.product_id}', status='{self.status}')>"


class GeneratedAsset(Base):
    """
    Generated media assets from the media studio.
    """
    __tablename__ = 'generated_assets'

    id = Column(String, primary_key=True, index=True)  # UUID
    workspace_id = Column(Integer, ForeignKey('tenants.id'), nullable=False)
    product_id = Column(String, nullable=False)
    type = Column(String, nullable=False)  # 'image' or 'video'
    file_uri = Column(String, nullable=False)
    preview_uri = Column(String, nullable=True)
    prompt = Column(Text, nullable=True)
    model = Column(String, nullable=True)
    settings = Column(JSON, nullable=True)

    # Timestamps
    created_at = Column(DateTime(timezone=True), server_default=func.now())
    updated_at = Column(DateTime(timezone=True), onupdate=func.now())

    # Relationships
    workspace = relationship("Tenant", back_populates="generated_assets")

    def __repr__(self):
        return f"<GeneratedAsset(id='{self.id}', product_id='{self.product_id}', type='{self.type}')>"
