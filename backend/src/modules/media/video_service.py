"""
Video Service - Wrapper for video generation using the unified media generation service
"""

import logging
from typing import Optional, Dict, Any, List
from pydantic import BaseModel

from .service import media_service
from .schemas import MediaGenerationRequest, MediaGenerationResult

logger = logging.getLogger(__name__)


class VideoGenerationRequest(BaseModel):
    """Request model for video generation."""
    product_id: str
    template_id: Optional[str] = None
    voice_id: Optional[str] = None
    script: Optional[str] = None
    aspect_ratio: str = "16:9"
    variants_count: int = 4
    custom_config: Optional[Dict[str, Any]] = None


class VideoGenerationResult(BaseModel):
    """Result model for video generation."""
    success: bool
    variants: Optional[List[Dict[str, Any]]] = None
    error_message: Optional[str] = None


class VideoService:
    """Service for AI video generation."""

    async def generate(self, request: MediaGenerationRequest) -> MediaGenerationResult:
        """Generate videos using the unified media generation service."""
        try:
            # Create video-specific request
            video_request = MediaGenerationRequest(
                media_type="video",
                product_id=request.product_id,
                template_id=request.template_id,
                voice_id=getattr(request, 'voice_id', None),
                text_input=getattr(request, 'script', None),
                custom_config={
                    "aspect_ratio": request.aspect_ratio or "16:9",
                    "variants_count": getattr(request, 'variants_count', 4),
                    **(request.custom_config or {})
                }
            )

            # Get video provider
            provider_name = media_service._get_provider_for_media_type("video")

            # Generate using unified service
            result = await media_service.generate_media_with_provider(
                provider_name, video_request
            )

            return result

        except Exception as e:
            logger.error(f"Video generation failed: {e}")
            return MediaGenerationResult(
                success=False,
                error_message=str(e)
            )

    async def download_media(self, media_url: str) -> bytes:
        """Download media content from URL."""
        import aiohttp

        async with aiohttp.ClientSession() as session:
            async with session.get(media_url) as response:
                if response.status == 200:
                    return await response.read()
                else:
                    raise Exception(f"Failed to download media: {response.status}")


# Create service instance
ai_video_service = VideoService()