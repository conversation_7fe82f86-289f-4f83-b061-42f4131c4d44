"""
Media generation and processing Celery tasks.

This module contains all tasks related to generating and processing
media content like videos and images.
"""

import logging
from typing import Dict, Any, Optional, List

from servers.worker.celery_app import celery_app
from servers.worker.base_task import LoggedTask

logger = logging.getLogger(__name__)


@celery_app.task(name='media.generate_product_video', bind=True, base=LoggedTask, max_retries=3)
def generate_product_video(self, product_id: int, video_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate a video for a product using AI video generation services.
    
    Args:
        product_id: ID of the product to generate video for
        video_config: Configuration for video generation
        
    Returns:
        Video generation results
    """
    from servers.worker.processors.media_processor import MediaProcessor

    try:
        processor = MediaProcessor()
        result = processor.generate_product_video(product_id, video_config)
        
        logger.info(f"Video generation completed for product {product_id}")
        return result
        
    except Exception as e:
        logger.error(f"Video generation failed for product {product_id}: {e}", exc_info=True)
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 60  # 1min, 2min, 4min
            logger.info(f"Retrying video generation for product {product_id} in {countdown}s")
            raise self.retry(countdown=countdown, exc=e)
        
        raise


@celery_app.task(name='media.generate_product_images', bind=True, base=LoggedTask, max_retries=3)
def generate_product_images(self, product_id: int, image_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate images for a product using AI image generation services.
    
    Args:
        product_id: ID of the product to generate images for
        image_config: Configuration for image generation
        
    Returns:
        Image generation results
    """
    from servers.worker.processors.media_processor import MediaProcessor

    try:
        processor = MediaProcessor()
        result = processor.generate_product_images(product_id, image_config)
        
        logger.info(f"Image generation completed for product {product_id}")
        return result
        
    except Exception as e:
        logger.error(f"Image generation failed for product {product_id}: {e}", exc_info=True)
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 30  # 30s, 1min, 2min
            logger.info(f"Retrying image generation for product {product_id} in {countdown}s")
            raise self.retry(countdown=countdown, exc=e)
        
        raise


@celery_app.task(name='media.push_media_to_shopify', bind=True, base=LoggedTask, max_retries=5)
def push_media_to_shopify(self, media_id: int, store_id: int) -> Dict[str, Any]:
    """
    Push generated media back to Shopify store.
    
    Args:
        media_id: ID of the media to push
        store_id: ID of the store to push to
        
    Returns:
        Push operation results
    """
    from servers.worker.processors.media_push_processor import MediaPushProcessor
    
    try:
        processor = MediaPushProcessor()
        result = processor.push_media_to_shopify(media_id, store_id)
        
        logger.info(f"Media {media_id} pushed to store {store_id}")
        return result
        
    except Exception as e:
        logger.error(f"Media push failed for media {media_id} to store {store_id}: {e}", exc_info=True)
        
        # Retry with exponential backoff
        if self.request.retries < self.max_retries:
            countdown = 2 ** self.request.retries * 30  # 30s, 1min, 2min, 4min, 8min
            logger.info(f"Retrying media push for media {media_id} in {countdown}s")
            raise self.retry(countdown=countdown, exc=e)
        
        raise


@celery_app.task(name='media.batch_generate_videos', base=LoggedTask)
def batch_generate_videos(product_ids: List[int], video_config: Dict[str, Any]) -> Dict[str, Any]:
    """
    Generate videos for multiple products in batch.
    
    Args:
        product_ids: List of product IDs to generate videos for
        video_config: Configuration for video generation
        
    Returns:
        Batch generation results
    """
    results = []
    
    for product_id in product_ids:
        try:
            # Trigger individual video generation task
            task = generate_product_video.delay(product_id, video_config)
            results.append({
                'product_id': product_id,
                'task_id': task.id,
                'status': 'queued'
            })
            
        except Exception as e:
            logger.error(f"Error queuing video generation for product {product_id}: {e}")
            results.append({
                'product_id': product_id,
                'status': 'failed',
                'error': str(e)
            })
    
    logger.info(f"Queued video generation for {len(results)} products")
    return {
        'total_products': len(product_ids),
        'queued_count': len([r for r in results if r['status'] == 'queued']),
        'failed_count': len([r for r in results if r['status'] == 'failed']),
        'results': results
    }


@celery_app.task(name='media.cleanup_old_media', base=LoggedTask)
def cleanup_old_media(days_old: int = 90):
    """
    Clean up old generated media files to save storage space.
    
    Args:
        days_old: Number of days old media to keep
    """
    from core.db.database import SessionLocal
    from datetime import datetime, timezone, timedelta
    import os
    
    db = SessionLocal()
    
    try:
        cutoff_date = datetime.now(timezone.utc) - timedelta(days=days_old)
        
        # This would typically query a media table and delete old files
        # For now, just log the operation
        logger.info(f"Would clean up media files older than {cutoff_date}")
        
        # TODO: Implement actual media cleanup logic
        # - Query media records older than cutoff_date
        # - Delete files from storage
        # - Remove database records
        
        return {'status': 'completed', 'message': 'Media cleanup not yet implemented'}
        
    except Exception as e:
        logger.error(f"Error cleaning up old media: {e}", exc_info=True)
        raise
    finally:
        db.close()


