import json
import logging
import socket
import time
from contextlib import asynccontextmanager

from fastapi import FastAPI, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse

import core.db.models  # Import all models to register them
from core.config import get_settings

from core.middleware.request_context_middleware import RequestContextMiddleware
from core.middleware.request_logging_middleware import request_logging_middleware
from core.utils.logging import setup_logging

# Import module routers
from modules.auth.router import router as auth_router
from modules.stores.router import router as stores_router
from modules.media.router import router as media_router
from modules.analytics.router import router as analytics_router
from modules.storage.router import router as storage_router
from modules.templates.router import router as templates_router
from modules.billing.router import router as billing_router
from modules.products.router import router as products_router
from modules.customers.router import router as customers_router
from modules.scraper.router import router as scraper_router
from prometheus_client import generate_latest, CONTENT_TYPE_LATEST
from fastapi.responses import Response

# Import plugin system
from plugins import register_plugins
# from modules.billing.router import router as billing_router

# Setup logging as early as possible
setup_logging()

logger = logging.getLogger(__name__)


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    settings = get_settings()
    
    # Startup events
    host = "localhost"
    port = settings.PORT

    try:
        network_ip = socket.gethostbyname(socket.gethostname())
    except socket.gaierror:
        network_ip = "<network_ip>"

    logger.info("\n" + "=" * 40)
    logger.info("🚀 E-commerce Integration Backend Started")
    logger.info(f"- Local:   http://{host}:{port}")
    logger.info(f"- Network: http://{network_ip}:{port}")
    logger.info(f"- Environment: {settings.ENVIRONMENT}")
    logger.info("=" * 40 + "\n")

    yield

    # Shutdown events
    logger.info("🛑 E-commerce Integration Backend Shutting Down")


app = FastAPI(
    title="E-commerce Integration API",
    description="API for integrating with Shopify and WooCommerce stores",
    version="1.0.0",
    lifespan=lifespan
)

# Add the request context middleware
app.add_middleware(RequestContextMiddleware)

# Add CORS middleware
settings = get_settings()
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Request Logging Middleware
app.middleware("http")(request_logging_middleware)

# Include module routers
app.include_router(auth_router, prefix="/api/auth", tags=["authentication"])
app.include_router(stores_router, prefix="/api/stores", tags=["stores"])
app.include_router(media_router, prefix="/api/media", tags=["media"])
app.include_router(analytics_router, prefix="/api/analytics", tags=["analytics"])
app.include_router(storage_router, prefix="/api/storage", tags=["storage"])
app.include_router(templates_router, prefix="/api/templates", tags=["templates"])
app.include_router(billing_router, prefix="/api/billing", tags=["billing"])
app.include_router(products_router, prefix="/api/products", tags=["products"])
app.include_router(customers_router, prefix="/api/customers", tags=["customers"])
app.include_router(scraper_router, prefix="/api/scraper", tags=["scraper"])

# Register all plugins automatically
register_plugins(app)


# Log all registered routes
registered_routes = []
for route in app.routes:
    if hasattr(route, "path") and hasattr(route, "name"):
        registered_routes.append({"path": route.path, "name": route.name})
registered_routes_sorted = sorted(registered_routes, key=lambda x: x["path"])
# print("Registered API Routes:\n" + json.dumps(registered_routes_sorted, indent=2))


@app.get("/")
async def read_root():
    """Root endpoint."""
    return {
        "message": "E-commerce Integration API", 
        "version": "1.0.0",
        "environment": settings.ENVIRONMENT
    }


@app.get("/health")
async def health_check():
    """Health check endpoint for monitoring."""
    from datetime import datetime
    from core.db.database import SessionLocal
    from sqlalchemy import text

    try:
        # Check database connection
        db = SessionLocal()
        db.execute(text("SELECT 1"))
        db.close()

        return {
            "status": "healthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "service": "E-commerce Integration API",
            "database": "connected"
        }
    except Exception as e:
        logger.error(f"Health check failed: {e}")
        return {
            "status": "unhealthy",
            "timestamp": datetime.utcnow().isoformat(),
            "version": "1.0.0",
            "service": "E-commerce Integration API",
            "database": "disconnected",
            "error": str(e)
        }


@app.get("/metrics")
async def metrics():
    """Prometheus metrics endpoint."""
    return Response(generate_latest(), media_type=CONTENT_TYPE_LATEST)


@app.get("/status")
async def get_status():
    """Get detailed service status and statistics."""
    from datetime import datetime
    from core.db.database import SessionLocal
    from sqlalchemy import text

    try:
        # Get database stats
        db = SessionLocal()
        result = db.execute(text("SELECT COUNT(*) as store_count FROM stores"))
        store_count = result.scalar()
        db.close()

        return {
            "timestamp": datetime.utcnow().isoformat(),
            "service": "E-commerce Integration API",
            "version": "1.0.0",
            "database": {
                "stores_count": store_count
            },
            "environment": settings.ENVIRONMENT
        }
    except Exception as e:
        logger.error(f"Status check failed: {e}")
        return {
            "timestamp": datetime.utcnow().isoformat(),
            "service": "E-commerce Integration API",
            "error": str(e)
        }


@app.get("/queue/stats")
async def get_queue_stats():
    """Get comprehensive Celery queue statistics (Flower-like monitoring)."""
    from core.services.queue_service import celery_service

    try:
        stats = celery_service.get_queue_stats()
        return stats
    except Exception as e:
        logger.error(f"Queue stats endpoint failed: {e}")
        return {
            "error": str(e),
            "timestamp": datetime.utcnow().isoformat(),
            "message": "Failed to retrieve queue statistics"
        }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler."""
    logger.error(f"Global exception handler caught: {exc}", exc_info=True)
    return JSONResponse(
        status_code=500,
        content={"detail": "Internal server error"}
    )


if __name__ == "__main__":
    import uvicorn
    
    settings = get_settings()
    uvicorn.run(
        "src.servers.api.main:app",
        host="0.0.0.0",
        port=settings.PORT,
        reload=settings.ENVIRONMENT == "development"
    )
