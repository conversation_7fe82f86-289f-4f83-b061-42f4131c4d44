import logging
import sys

from pythonjsonlogger import jsonlogger

from core.config import get_settings


def setup_logging():
    """
    Sets up structured JSON logging based on configuration.
    This overrides Celery's default logging configuration.
    """
    settings = get_settings()

    # Get the root logger and configure it
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    # Clear ALL existing handlers from root logger
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)

    # Also clear handlers from specific loggers that might conflict
    celery_logger = logging.getLogger('celery')
    for handler in celery_logger.handlers[:]:
        celery_logger.removeHandler(handler)

    worker_logger = logging.getLogger('celery.worker')
    for handler in worker_logger.handlers[:]:
        worker_logger.removeHandler(handler)

    # Create our custom handler
    log_handler = logging.StreamHandler(sys.stdout)
    log_handler.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))

    if settings.LOG_FORMAT.lower() == "json":
        # Create a JSON formatter with module, filename, line number, process ID, and process name
        formatter = jsonlogger.JsonFormatter(
            "%(asctime)s %(name)s %(levelname)s %(pathname)s %(lineno)d %(process)d %(processName)s %(message)s"
        )
    else:
        # Use enhanced text formatter with module, filename, line number, process ID, and process name
        formatter = logging.Formatter(
            "%(asctime)s - %(name)s - %(levelname)s - %(pathname)s:%(lineno)d - PID:%(process)d [%(processName)s] - %(message)s"
        )

    log_handler.setFormatter(formatter)
    root_logger.addHandler(log_handler)

    # Ensure Celery uses our logging configuration
    celery_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    celery_logger.propagate = True  # Let messages propagate to root logger

    worker_logger.setLevel(getattr(logging, settings.LOG_LEVEL.upper(), logging.INFO))
    worker_logger.propagate = True

    # Set specific loggers to appropriate levels
    logging.getLogger("uvicorn.access").setLevel(logging.WARNING)
    logging.getLogger("sqlalchemy.engine").setLevel(logging.WARNING)
    logging.getLogger("celery.app.trace").setLevel(logging.WARNING)

    # Log that structured logging has been set up
    root_logger.info("Structured logging initialized", extra={
        'log_format': settings.LOG_FORMAT,
        'log_level': settings.LOG_LEVEL
    })
